package com.yunqu.park.iot.netty.manager;

import com.yunqu.park.iot.constant.IotLogMarkers;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备连接管理器
 * 用于管理设备连接和发送指令
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceConnectionManager {

    // 存储IMEI与Channel的映射关系
    private final ConcurrentHashMap<String, Channel> deviceChannels = new ConcurrentHashMap<>();
    
    // 存储Channel与IMEI的映射关系
    private final ConcurrentHashMap<String, String> channelImeiMap = new ConcurrentHashMap<>();

    /**
     * 注册设备连接
     * @param imei 设备IMEI号
     * @param channel 网络通道
     */
    public void registerDevice(String imei, Channel channel) {
        try {
            String channelId = channel.id().asShortText();
            String remoteAddress = channel.remoteAddress().toString();

            log.info("[CONNECTION-REGISTER] Registering device connection: IMEI={}, ChannelId={}, RemoteAddress={}",
                    imei, channelId, remoteAddress);

            // 移除旧的连接（如果存在）
            Channel oldChannel = deviceChannels.get(imei);
            if (oldChannel != null) {
                String oldChannelId = oldChannel.id().asShortText();
                log.warn("[CONNECTION-REGISTER] Removing old connection: IMEI={}, OldChannelId={}", imei, oldChannelId);
                removeDeviceConnection(imei);
            }

            // 注册新连接
            deviceChannels.put(imei, channel);
            channelImeiMap.put(channelId, imei);

            log.info(IotLogMarkers.IOT_CONNECTION,
                    "[CONNECTION-REGISTER] ✅ Device connection registered successfully: IMEI={}, ChannelId={}, TotalConnections={}",
                    imei, channelId, deviceChannels.size());
        } catch (Exception e) {
            log.error("[CONNECTION-REGISTER] ❌ Failed to register device connection: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
        }
    }

    /**
     * 移除设备连接
     * @param imei 设备IMEI号
     */
    public void removeDeviceConnection(String imei) {
        try {
            log.debug("[CONNECTION-REMOVE] Removing device connection: IMEI={}", imei);

            Channel oldChannel = deviceChannels.remove(imei);
            if (oldChannel != null) {
                String oldChannelId = oldChannel.id().asShortText();
                String remoteAddress = oldChannel.remoteAddress() != null ? oldChannel.remoteAddress().toString() : "unknown";
                channelImeiMap.remove(oldChannelId);

                log.info(IotLogMarkers.IOT_CONNECTION,
                        "[CONNECTION-REMOVE] ✅ Device connection removed: IMEI={}, ChannelId={}, RemoteAddress={}, RemainingConnections={}",
                        imei, oldChannelId, remoteAddress, deviceChannels.size());
            } else {
                log.debug("[CONNECTION-REMOVE] No connection found for device: IMEI={}", imei);
            }
        } catch (Exception e) {
            log.error("[CONNECTION-REMOVE] ❌ Failed to remove device connection: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
        }
    }

    /**
     * 通过Channel移除设备连接
     * @param channel 网络通道
     */
    public void removeDeviceByChannel(Channel channel) {
        try {
            String channelId = channel.id().asShortText();
            String imei = channelImeiMap.remove(channelId);
            if (imei != null) {
                deviceChannels.remove(imei);
                log.info("Device {} connection removed by channel {}", imei, channelId);
            }
        } catch (Exception e) {
            log.error("Failed to remove device by channel: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取设备连接通道
     * @param imei 设备IMEI号
     * @return 网络通道
     */
    public Channel getDeviceChannel(String imei) {
        return deviceChannels.get(imei);
    }

    /**
     * 通过Channel获取设备IMEI
     * @param channel 网络通道
     * @return 设备IMEI号
     */
    public String getDeviceImei(Channel channel) {
        String channelId = channel.id().asShortText();
        return channelImeiMap.get(channelId);
    }

    /**
     * 检查设备是否在线
     * @param imei 设备IMEI号
     * @return 是否在线
     */
    public boolean isDeviceOnline(String imei) {
        Channel channel = deviceChannels.get(imei);
        return channel != null && channel.isActive();
    }

    /**
     * 发送指令到设备
     * @param imei 设备IMEI号
     * @param command 指令内容
     * @return 是否发送成功
     */
    public boolean sendCommandToDevice(String imei, String command) {
        try {
            log.info("[COMMAND-SEND] Preparing to send command: IMEI={}, Command={}", imei, command);

            Channel channel = deviceChannels.get(imei);
            if (channel == null) {
                log.warn("[COMMAND-SEND] ❌ Device not connected: IMEI={}", imei);
                return false;
            }

            if (!channel.isActive()) {
                log.warn("[COMMAND-SEND] ❌ Channel is inactive: IMEI={}, ChannelId={}",
                        imei, channel.id().asShortText());
                return false;
            }

            // 构建指令数据包
            byte[] commandBytes = buildCommandPacket(command);
            log.debug("[COMMAND-SEND] Command packet built: IMEI={}, PacketLength={}", imei, commandBytes.length);

            // 发送指令
            channel.writeAndFlush(Unpooled.wrappedBuffer(commandBytes));

            log.info(IotLogMarkers.IOT_COMMAND,
                    "[COMMAND-SEND] ✅ Command sent successfully: IMEI={}, Command={}, ChannelId={}",
                    imei, command, channel.id().asShortText());
            return true;

        } catch (Exception e) {
            log.error("[COMMAND-SEND] ❌ Failed to send command: IMEI={}, Command={}, Error={}",
                     imei, command, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送原始字节数据到设备
     * @param imei 设备IMEI号
     * @param data 原始数据
     * @return 是否发送成功
     */
    public boolean sendRawDataToDevice(String imei, byte[] data) {
        try {
            Channel channel = deviceChannels.get(imei);
            if (channel == null || !channel.isActive()) {
                log.warn("Device {} is not connected or channel is inactive", imei);
                return false;
            }

            // 发送原始数据
            channel.writeAndFlush(Unpooled.wrappedBuffer(data));
            
            log.debug("Raw data sent to device {}, length: {}", imei, data.length);
            return true;
            
        } catch (Exception e) {
            log.error("Failed to send raw data to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取在线设备数量
     * @return 在线设备数量
     */
    public int getOnlineDeviceCount() {
        return (int) deviceChannels.values().stream()
                .filter(Channel::isActive)
                .count();
    }

    /**
     * 获取所有在线设备IMEI列表
     * @return 在线设备IMEI列表
     */
    public java.util.Set<String> getOnlineDeviceImeis() {
        return deviceChannels.entrySet().stream()
                .filter(entry -> entry.getValue().isActive())
                .map(java.util.Map.Entry::getKey)
                .collect(java.util.stream.Collectors.toSet());
    }

    /**
     * 构建指令数据包
     * 根据GT06协议格式构建指令包
     * @param command 指令内容
     * @return 指令数据包
     */
    private byte[] buildCommandPacket(String command) {
        try {
            // GT06协议指令格式：起始位(2) + 包长度(1) + 协议号(1) + 指令内容 + 序列号(2) + CRC(2) + 停止位(2)
            
            // 指令内容转字节
            byte[] commandBytes = command.getBytes("UTF-8");
            
            // 计算包长度（协议号 + 指令内容 + 序列号 + CRC）
            int packetLength = 1 + commandBytes.length + 2 + 2;
            
            // 构建数据包
            byte[] packet = new byte[2 + 1 + packetLength + 2]; // 起始位 + 包长度 + 内容 + 停止位
            int index = 0;
            
            // 起始位 0x7878
            packet[index++] = 0x78;
            packet[index++] = 0x78;
            
            // 包长度
            packet[index++] = (byte) packetLength;
            
            // 协议号（服务器指令下发）
            packet[index++] = (byte) 0x80;
            
            // 指令内容
            System.arraycopy(commandBytes, 0, packet, index, commandBytes.length);
            index += commandBytes.length;
            
            // 序列号（简单递增）
            packet[index++] = 0x00;
            packet[index++] = 0x01;
            
            // CRC校验（简化处理，实际应该计算CRC-ITU）
            packet[index++] = 0x00;
            packet[index++] = 0x00;
            
            // 停止位 0x0D0A
            packet[index++] = 0x0D;
            packet[index] = 0x0A;
            
            return packet;
            
        } catch (Exception e) {
            log.error("Failed to build command packet: {}", e.getMessage(), e);
            return new byte[0];
        }
    }
}
