# GT06物联网设备协议对接详细设计文档

## 1. 项目概述

### 1.1 项目背景
基于R16-E动态物联网SP(GT06)中文协议1.02版本，实现车载GPS定位器与服务器平台的通信对接，支持设备登录、定位数据上报、心跳监控、报警处理等核心功能。

### 1.2 技术架构
- **框架版本**: Spring Boot 3.4.4 + JDK 17
- **通信协议**: TCP/IP + 自定义二进制协议
- **网络框架**: Netty 4.x
- **数据存储**: MySQL 8.0 + Redis 7.x
- **实时推送**: WebSocket + SSE

## 2. 协议分析

### 2.1 数据包格式
### 2.1 数据包格式
```
| 起始位(2) | 包长度(1) | 协议号(1) | 信息内容(N) | 信息序列号(2) | 错误校验(2) | 停止位(2) |
|   0x7878  |    0x0D   |   0x01    |    ...      |    0x0001     |   CRC-ITU   |  0x0D0A   |
|   0x7979  |    0x20   |   0x94    |    ...      |    0x0001     |   CRC-ITU   |  0x0D0A   |
```
**注意**: 大部分协议使用0x7878起始位，少部分协议(0x94、0x8D)使用0x7979起始位

### 2.2 核心协议类型及响应规范
- **0x01**: 登录包 - 设备认证和连接建立 ✅**需要响应**
- **0x12**: 定位数据包 - GPS/LBS位置信息 ❌**无需响应**
- **0x13**: 心跳包 - 连接保活和状态监控 ✅**需要响应**
- **0x16**: 报警包 - 各类报警事件 ✅**需要响应**
- **0x1A**: 查询地址信息 ❌**无需响应**
- **0x18**: LBS多基站信息 ❌**无需响应**
- **0x2C**: LBS+WIFI信息 ❌**无需响应**
- **0x90**: IMSI号上报 ❌**无需响应**
- **0x94**: ICCID号上报(0x7979起始) ❌**无需响应**
- **0x8D**: 录音协议包(0x7979起始) ✅**需要响应**
- **0x80**: 服务器指令下发 ✅**终端响应0x15**
- **0x15**: 终端指令响应

### 2.3 CRC-ITU校验算法
### 2.3 CRC-ITU校验算法
采用标准CRC-ITU算法，校验范围从"包长度"到"信息序列号"。

```java
/**
 * CRC-ITU校验算法实现 - 基于协议文档提供的C语言代码
 */
public class CrcUtils {
    
    private static final int[] CRC_TAB_16 = {
        0x0000, 0x1189, 0x2312, 0x329B, 0x4624, 0x57AD, 0x6536, 0x74BF,
        0x8C48, 0x9DC1, 0xAF5A, 0xBED3, 0xCA6C, 0xDBE5, 0xE97E, 0xF8F7,
        0x1081, 0x0108, 0x3393, 0x221A, 0x56A5, 0x472C, 0x75B7, 0x643E,
        0x9CC9, 0x8D40, 0xBFDB, 0xAE52, 0xDAED, 0xCB64, 0xF9FF, 0xE876,
        0x2102, 0x308B, 0x0210, 0x1399, 0x6726, 0x76AF, 0x4434, 0x55BD,
        0xAD4A, 0xBCC3, 0x8E58, 0x9FD1, 0xEB6E, 0xFAE7, 0xC87C, 0xD9F5,
        0x3183, 0x200A, 0x1291, 0x0318, 0x77A7, 0x662E, 0x54B5, 0x453C,
        0xBDCB, 0xAC42, 0x9ED9, 0x8F50, 0xFBEF, 0xEA66, 0xD8FD, 0xC974,
        0x4204, 0x538D, 0x6116, 0x709F, 0x0420, 0x15A9, 0x2732, 0x36BB,
        0xCE4C, 0xDFC5, 0xED5E, 0xFCD7, 0x8868, 0x99E1, 0xAB7A, 0xBAF3,
        0x5285, 0x430C, 0x7197, 0x601E, 0x14A1, 0x0528, 0x37B3, 0x263A,
        0xDECD, 0xCF44, 0xFDDF, 0xEC56, 0x98E9, 0x8960, 0xBBFB, 0xAA72,
        0x6306, 0x728F, 0x4014, 0x519D, 0x2522, 0x34AB, 0x0630, 0x17B9,
        0xEF4E, 0xFEC7, 0xCC5C, 0xDDD5, 0xA96A, 0xB8E3, 0x8A78, 0x9BF1,
        0x7387, 0x620E, 0x5095, 0x411C, 0x35A3, 0x242A, 0x16B1, 0x0738,
        0xFFCF, 0xEE46, 0xDCDD, 0xCD54, 0xB9EB, 0xA862, 0x9AF9, 0x8B70,
        0x8408, 0x9581, 0xA71A, 0xB693, 0xC22C, 0xD3A5, 0xE13E, 0xF0B7,
        0x0840, 0x19C9, 0x2B52, 0x3ADB, 0x4E64, 0x5FED, 0x6D76, 0x7CFF,
        0x9489, 0x8500, 0xB79B, 0xA612, 0xD2AD, 0xC324, 0xF1BF, 0xE036,
        0x18C1, 0x0948, 0x3BD3, 0x2A5A, 0x5EE5, 0x4F6C, 0x7DF7, 0x6C7E,
        0xA50A, 0xB483, 0x8618, 0x9791, 0xE32E, 0xF2A7, 0xC03C, 0xD1B5,
        0x2942, 0x38CB, 0x0A50, 0x1BD9, 0x6F66, 0x7EEF, 0x4C74, 0x5DFD,
        0xB58B, 0xA402, 0x9699, 0x8710, 0xF3AF, 0xE226, 0xD0BD, 0xC134,
        0x39C3, 0x284A, 0x1AD1, 0x0B58, 0x7FE7, 0x6E6E, 0x5CF5, 0x4D7C,
        0xC60C, 0xD785, 0xE51E, 0xF497, 0x8028, 0x91A1, 0xA33A, 0xB2B3,
        0x4A44, 0x5BCD, 0x6956, 0x78DF, 0x0C60, 0x1DE9, 0x2F72, 0x3EFB,
        0xD68D, 0xC704, 0xF59F, 0xE416, 0x90A9, 0x8120, 0xB3BB, 0xA232,
        0x5AC5, 0x4B4C, 0x79D7, 0x685E, 0x1CE1, 0x0D68, 0x3FF3, 0x2E7A,
        0xE70E, 0xF687, 0xC41C, 0xD595, 0xA12A, 0xB0A3, 0x8238, 0x93B1,
        0x6B46, 0x7ACF, 0x4854, 0x59DD, 0x2D62, 0x3CEB, 0x0E70, 0x1FF9,
        0xF78F, 0xE606, 0xD49D, 0xC514, 0xB1AB, 0xA022, 0x92B9, 0x8330,
        0x7BC7, 0x6A4E, 0x58D5, 0x495C, 0x3DE3, 0x2C6A, 0x1EF1, 0x0F78
    };
    
    /**
     * 计算给定数据的16位CRC-ITU校验值
     * @param data 待校验的数据
     * @return CRC校验值
     */
    public static int calculateCrcItu(byte[] data) {
        int fcs = 0xFFFF; // 初始化
        
        for (byte b : data) {
            fcs = (fcs >> 8) ^ CRC_TAB_16[(fcs ^ (b & 0xFF)) & 0xFF];
        }
        
        return (~fcs) & 0xFFFF; // 取反并确保16位
    }
    
    /**
     * 验证CRC校验值
     * @param data 包含CRC的完整数据包(不含起始位和停止位)
     * @return 校验是否通过
     */
    public static boolean validateCrc(byte[] data) {
        if (data.length < 4) {
            return false;
        }
        
        // 分离数据和CRC
        byte[] dataOnly = Arrays.copyOf(data, data.length - 2);
        int receivedCrc = ((data[data.length - 2] & 0xFF) << 8) | 
                         (data[data.length - 1] & 0xFF);
        
        // 计算CRC并比较
        int calculatedCrc = calculateCrcItu(dataOnly);
        return calculatedCrc == receivedCrc;
    }
}
```

## 3. 系统架构设计

### 3.1 模块结构
### 3.1 模块结构
```
park-modules/park-iot/
├── pom.xml
├── src/main/java/com/yunqu/park/iot/
│   ├── config/          # 配置类
│   ├── constant/        # 常量定义
│   ├── domain/          # 实体类
│   │   ├── bo/          # 业务对象(Business Object)
│   │   ├── vo/          # 视图对象(View Object)
│   │   └── IotDevice.java # 数据库实体
│   ├── enums/           # 枚举类
│   ├── mapper/          # 数据访问层
│   ├── service/         # 业务服务层
│   │   └── impl/        # 服务实现类
│   ├── controller/      # 控制器层
│   ├── netty/           # Netty网络层
│   │   ├── server/      # TCP服务器
│   │   ├── handler/     # 消息处理器
│   │   ├── codec/       # 编解码器
│   │   └── protocol/    # 协议解析
│   └── utils/           # 工具类
└── src/main/resources/
    ├── mapper/iot/      # MyBatis映射文件
    └── application-iot.yml
```

**注意**: 按照项目规范，IoT模块应放在`park-modules`目录下，与其他业务模块保持一致的结构。

### 3.2 核心组件设计

#### 3.2.1 Netty服务器架构
```java
// TCP服务器启动器
@Component
public class IotTcpServer {
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ServerBootstrap bootstrap;
    
    // 服务器启动配置
    // 通道初始化器配置
    // 优雅关闭处理
}

// 通道处理器链
public class IotChannelInitializer extends ChannelInitializer<SocketChannel> {
    @Override
    protected void initChannel(SocketChannel ch) {
        ch.pipeline()
          .addLast("decoder", new GT06ProtocolDecoder())
          .addLast("encoder", new GT06ProtocolEncoder())
          .addLast("handler", new IotMessageHandler());
    }
}
```

#### 3.2.2 协议编解码器
#### 3.2.2 协议编解码器
```java
// GT06协议解码器
public class GT06ProtocolDecoder extends ByteToMessageDecoder {
    
    private static final byte[] START_FLAG_7878 = {0x78, 0x78};
    private static final byte[] START_FLAG_7979 = {0x79, 0x79};
    private static final byte[] STOP_FLAG = {0x0D, 0x0A};
    
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        if (in.readableBytes() < 10) { // 最小包长度
            return;
        }
        
        in.markReaderIndex();
        
        // 1. 检查起始位 (支持0x7878和0x7979)
        byte[] startFlag = new byte[2];
        in.readBytes(startFlag);
        
        if (!Arrays.equals(startFlag, START_FLAG_7878) && 
            !Arrays.equals(startFlag, START_FLAG_7979)) {
            in.resetReaderIndex();
            in.skipBytes(1); // 跳过一个字节继续寻找
            return;
        }
        
        // 2. 读取包长度
        byte packetLength = in.readByte();
        int totalLength = packetLength + 5; // 包长度不包含起始位和停止位
        
        // 3. 验证数据完整性
        if (in.readableBytes() < totalLength - 3) {
            in.resetReaderIndex();
            return;
        }
        
        // 4. 读取完整数据包
        byte[] packetData = new byte[totalLength - 2];
        in.readBytes(packetData);
        
        // 5. 验证停止位
        byte[] stopFlag = new byte[2];
        in.readBytes(stopFlag);
        if (!Arrays.equals(stopFlag, STOP_FLAG)) {
            throw new ProtocolException("Invalid stop flag");
        }
        
        // 6. CRC校验
        if (!validateCrc(packetData)) {
            throw new CrcValidationException("CRC validation failed");
        }
        
        // 7. 解析协议内容并构造消息对象
        IotMessage message = parseMessage(startFlag, packetData);
        out.add(message);
    }
    
    /**
     * 经纬度解析 - 按协议规范实现
     * 公式: (度数×60+分数)×30000 = 十进制值
     */
    public static double parseCoordinate(byte[] coordinateBytes) {
        int value = ByteBuffer.wrap(coordinateBytes).getInt();
        return value / 30000.0 / 60.0; // 转换回度数
    }
    
    /**
     * 状态航向解析 - 按协议规范实现
     */
    public static IotStatusDirection parseStatusDirection(byte[] statusBytes) {
        int byte1 = statusBytes[0] & 0xFF;
        int byte2 = statusBytes[1] & 0xFF;
        
        // 前6位表示状态
        boolean oilElectricCut = (byte1 & 0x80) != 0;
        boolean gpsPositioned = (byte1 & 0x40) != 0;
        int alarmType = (byte1 >> 3) & 0x07;
        boolean externalPower = (byte1 & 0x04) != 0;
        boolean accOn = (byte1 & 0x02) != 0;
        boolean armed = (byte1 & 0x01) != 0;
        
        // 后10位表示航向(0-360度)
        int direction = ((byte1 & 0x03) << 8) | byte2;
        
        return new IotStatusDirection(oilElectricCut, gpsPositioned, alarmType,
            externalPower, accOn, armed, direction);
    }
}

// GT06协议编码器
public class GT06ProtocolEncoder extends MessageToByteEncoder<IotMessage> {
    @Override
    protected void encode(ChannelHandlerContext ctx, IotMessage msg, ByteBuf out) {
        // 1. 写入起始位(根据协议类型选择)
        if (msg.getProtocol() == 0x94 || msg.getProtocol() == 0x8D) {
            out.writeBytes(new byte[]{0x79, 0x79});
        } else {
            out.writeBytes(new byte[]{0x78, 0x78});
        }
        
        // 2. 构建数据包内容
        ByteBuf content = Unpooled.buffer();
        content.writeByte(msg.getProtocol());
        if (msg.getContent() != null) {
            content.writeBytes(msg.getContent());
        }
        content.writeShort(msg.getSequenceNumber());
        
        // 3. 计算并写入包长度
        out.writeByte(content.readableBytes() + 2); // +2 for CRC
        
        // 4. 写入内容
        out.writeBytes(content);
        
        // 5. 计算CRC校验
        byte[] dataForCrc = new byte[out.readableBytes() - 2]; // 不包含起始位
        out.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        out.writeShort(crc);
        
        // 6. 写入停止位
        out.writeBytes(new byte[]{0x0D, 0x0A});
        
        content.release();
    }
    
    /**
     * 特殊设备登录响应(S11/S11C/W15L等)
     * 需要包含UTC时间信息
     */
    public static byte[] buildLoginResponseWithTime(int sequenceNumber) {
        LocalDateTime utcTime = LocalDateTime.now(ZoneOffset.UTC);
        
        ByteBuf response = Unpooled.buffer();
        response.writeBytes(new byte[]{0x78, 0x78}); // 起始位
        response.writeByte(0x0B); // 包长度
        response.writeByte(0x01); // 协议号
        response.writeShort(sequenceNumber); // 序列号
        
        // UTC时间 (年月日时分秒)
        response.writeByte(utcTime.getYear() % 100); // 年(后两位)
        response.writeByte(utcTime.getMonthValue()); // 月
        response.writeByte(utcTime.getDayOfMonth()); // 日
        response.writeByte(utcTime.getHour()); // 时
        response.writeByte(utcTime.getMinute()); // 分
        response.writeByte(utcTime.getSecond()); // 秒
        
        // 计算CRC
        byte[] dataForCrc = new byte[response.readableBytes() - 2];
        response.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        response.writeShort(crc);
        
        response.writeBytes(new byte[]{0x0D, 0x0A}); // 停止位
        
        byte[] result = new byte[response.readableBytes()];
        response.readBytes(result);
        response.release();
        
        return result;
    }
}
```

## 4. 数据库设计

### 4.1 设备基础信息表 (iot_device)
### 4.1 设备基础信息表 (iot_device)
```sql
CREATE TABLE iot_device (
    device_id BIGINT PRIMARY KEY COMMENT '设备ID',
    imei VARCHAR(15) UNIQUE NOT NULL COMMENT '设备IMEI号',
    device_name VARCHAR(100) COMMENT '设备名称',
    device_type VARCHAR(50) DEFAULT 'GT06' COMMENT '设备类型',
    sim_imsi VARCHAR(15) COMMENT 'SIM卡IMSI号',
    sim_iccid VARCHAR(20) COMMENT 'SIM卡ICCID号',
    status CHAR(1) DEFAULT '0' COMMENT '设备状态:0-离线,1-在线,2-休眠',
    last_online_time DATETIME COMMENT '最后在线时间',
    register_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
    remark VARCHAR(500) COMMENT '备注',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志(0代表存在 1代表删除)'
) COMMENT='IoT设备基础信息表';

-- 对应的实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_device")
public class IotDevice extends TenantEntity {

    @TableId(value = "device_id")
    private Long deviceId;

    @NotBlank(message = "设备IMEI号不能为空")
    @Size(min = 15, max = 15, message = "IMEI号必须为15位")
    private String imei;

    @Size(max = 100, message = "设备名称不能超过100个字符")
    private String deviceName;

    private String deviceType;

    @Size(max = 15, message = "IMSI号不能超过15位")
    private String simImsi;

    @Size(max = 20, message = "ICCID号不能超过20位")
    private String simIccid;

    private String status;

    private Date lastOnlineTime;

    private Date registerTime;

    private String remark;

    @TableLogic
    private String delFlag;
}
```

### 4.2 设备定位数据表 (iot_location_data)
### 4.2 设备定位数据表 (iot_location_data)
```sql
CREATE TABLE iot_location_data (
    location_id BIGINT PRIMARY KEY COMMENT '定位记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    gps_time DATETIME NOT NULL COMMENT 'GPS时间',
    latitude DECIMAL(10,6) COMMENT '纬度',
    longitude DECIMAL(10,6) COMMENT '经度',
    altitude INT COMMENT '海拔高度(米)',
    speed TINYINT COMMENT '速度(km/h)',
    direction SMALLINT COMMENT '方向角(0-360度)',
    satellite_count TINYINT COMMENT '卫星数量',
    gps_status CHAR(1) DEFAULT '0' COMMENT 'GPS状态:0-未定位,1-已定位',
    acc_status CHAR(1) DEFAULT '0' COMMENT 'ACC状态:0-关闭,1-开启',
    mcc SMALLINT COMMENT '移动国家代码',
    mnc TINYINT COMMENT '移动网络代码',
    lac SMALLINT COMMENT '位置区码',
    cell_id INT COMMENT '基站ID',
    signal_strength TINYINT COMMENT '信号强度(0-100)',
    mileage INT COMMENT '里程(米)',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_time DATETIME COMMENT '创建时间',
    INDEX idx_device_time (device_id, gps_time),
    INDEX idx_imei_time (imei, gps_time),
    INDEX idx_tenant_time (tenant_id, gps_time)
) COMMENT='设备定位数据表';

-- 对应的实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_location_data")
public class IotLocationData extends TenantEntity {

    @TableId(value = "location_id")
    private Long locationId;

    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    @NotBlank(message = "设备IMEI不能为空")
    private String imei;

    @NotNull(message = "GPS时间不能为空")
    private Date gpsTime;

    @DecimalMin(value = "-90.0", message = "纬度范围错误")
    @DecimalMax(value = "90.0", message = "纬度范围错误")
    private BigDecimal latitude;

    @DecimalMin(value = "-180.0", message = "经度范围错误")
    @DecimalMax(value = "180.0", message = "经度范围错误")
    private BigDecimal longitude;

    private Integer altitude;

    @Min(value = 0, message = "速度不能为负数")
    @Max(value = 255, message = "速度超出范围")
    private Integer speed;

    @Min(value = 0, message = "方向角不能为负数")
    @Max(value = 360, message = "方向角超出范围")
    private Integer direction;

    private Integer satelliteCount;

    private String gpsStatus;

    private String accStatus;

    private Integer mcc;

    private Integer mnc;

    private Integer lac;

    private Integer cellId;

    private Integer signalStrength;

    private Integer mileage;
}
```

### 4.3 设备报警记录表 (iot_alarm_record)
### 4.3 设备报警记录表 (iot_alarm_record)
```sql
CREATE TABLE iot_alarm_record (
    alarm_id BIGINT PRIMARY KEY COMMENT '报警记录ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    alarm_type TINYINT NOT NULL COMMENT '报警类型',
    alarm_name VARCHAR(50) COMMENT '报警名称',
    alarm_time DATETIME NOT NULL COMMENT '报警时间',
    latitude DECIMAL(10,6) COMMENT '报警位置纬度',
    longitude DECIMAL(10,6) COMMENT '报警位置经度',
    address VARCHAR(200) COMMENT '报警地址',
    alarm_status CHAR(1) DEFAULT '0' COMMENT '处理状态:0-未处理,1-已处理',
    handle_time DATETIME COMMENT '处理时间',
    handle_user BIGINT COMMENT '处理人',
    handle_remark VARCHAR(500) COMMENT '处理备注',
    tenant_id VARCHAR(20) COMMENT '租户编号',
    create_dept BIGINT COMMENT '创建部门',
    create_by BIGINT COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by BIGINT COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    INDEX idx_device_time (device_id, alarm_time),
    INDEX idx_type_status (alarm_type, alarm_status),
    INDEX idx_tenant_time (tenant_id, alarm_time)
) COMMENT='设备报警记录表';

-- 对应的实体类
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("iot_alarm_record")
public class IotAlarmRecord extends TenantEntity {

    @TableId(value = "alarm_id")
    private Long alarmId;

    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    @NotBlank(message = "设备IMEI不能为空")
    private String imei;

    @NotNull(message = "报警类型不能为空")
    private Integer alarmType;

    private String alarmName;

    @NotNull(message = "报警时间不能为空")
    private Date alarmTime;

    private BigDecimal latitude;

    private BigDecimal longitude;

    private String address;

    private String alarmStatus;

    private Date handleTime;

    private Long handleUser;

    private String handleRemark;
}
```

### 4.4 设备状态日志表 (iot_device_status_log)
```sql
CREATE TABLE iot_device_status_log (
    log_id BIGINT PRIMARY KEY COMMENT '日志ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    imei VARCHAR(15) NOT NULL COMMENT '设备IMEI',
    old_status TINYINT COMMENT '原状态',
    new_status TINYINT COMMENT '新状态',
    change_reason VARCHAR(100) COMMENT '状态变更原因',
    battery_level TINYINT COMMENT '电池电量等级',
    signal_strength TINYINT COMMENT '信号强度',
    external_voltage TINYINT COMMENT '外部电压',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_device_time (device_id, create_time)
) COMMENT='设备状态变更日志表';
```

## 5. 核心业务流程设计

### 5.1 设备登录流程
```mermaid
sequenceDiagram
    participant Device as GT06设备
    participant Server as TCP服务器
    participant Service as 设备服务
    participant DB as 数据库
    participant Cache as Redis缓存

    Device->>Server: 发送登录包(0x01)
    Server->>Server: 协议解析和CRC校验
    Server->>Service: 处理登录请求
    Service->>DB: 验证设备IMEI
    Service->>Cache: 缓存设备连接信息
    Service->>DB: 更新设备在线状态
    Service->>Server: 返回处理结果
    Server->>Device: 发送登录响应包
    Note over Device,Cache: 设备登录成功，建立长连接
```

### 5.2 定位数据处理流程
```mermaid
sequenceDiagram
    participant Device as GT06设备
    participant Server as TCP服务器
    participant Service as 定位服务
    participant DB as 数据库
    participant WS as WebSocket

    Device->>Server: 发送定位包(0x12)
    Server->>Server: 协议解析
    Server->>Service: 处理定位数据
    Service->>DB: 存储定位记录
    Service->>WS: 推送实时位置
    Note over Device,WS: 无需响应定位包
```

### 5.3 心跳监控流程
```mermaid
sequenceDiagram
    participant Device as GT06设备
    participant Server as TCP服务器
    participant Service as 心跳服务
    participant Cache as Redis缓存

    Device->>Server: 发送心跳包(0x13)
    Server->>Service: 处理心跳数据
    Service->>Cache: 更新设备状态
    Service->>Server: 返回心跳响应
    Server->>Device: 发送心跳响应包(0x13)
    
    Note over Service,Cache: 定时检查设备离线状态
    Service->>Service: 检测超时设备
    Service->>Cache: 标记设备离线
```

## 6. 配置参数设计

### 6.1 IoT服务配置 (application-iot.yml)
```yaml
# IoT设备服务配置
iot:
  # TCP服务器配置
  tcp-server:
    enabled: true
    port: 8888
    boss-threads: 1
    worker-threads: 8
    so-backlog: 128
    so-keepalive: true
    tcp-nodelay: true
    
  # 设备管理配置
  device:
    # 心跳超时时间(秒)
    heartbeat-timeout: 300
    # 设备离线检查间隔(秒)
    offline-check-interval: 60
    # 最大重连次数
    max-reconnect-times: 3
    # 重连间隔(秒)
    reconnect-interval: 20
    
  # 数据处理配置
  data:
    # 定位数据批量插入大小
    location-batch-size: 100
    # 数据保留天数
    data-retention-days: 365
    # 是否启用数据压缩
    enable-compression: true
    
  # 报警配置
    # 报警配置
  alarm:
    # 报警类型映射(完整版本)
    type-mapping:
      0x00: "正常"
      0x01: "SOS报警"
      0x02: "断电报警"
      0x03: "震动报警"
      0x04: "进围栏报警"
      0x05: "出围栏报警"
      0x06: "超速报警"
      0x07: "高温报警"
      0x08: "低温报警"
      0x09: "位移报警"
      0x0E: "低电报警"
      0x13: "防拆报警(光感报警)"
      0x26: "急加速报警"
      0x27: "急减速报警"
      0x28: "急转弯报警"
      0x29: "碰撞报警"
      0xFA: "门关闭报警"
      0xFB: "门打开报警"
      0xFC: "AC关闭报警"
      0xFD: "AC打开报警"
      0xFE: "ACC点火报警"
      0xFF: "ACC熄火报警"
```

## 7. API接口设计

### 7.1 设备管理接口
### 7.1 设备管理接口
```java
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/device")
public class IotDeviceController extends BaseController {
    
    private final IIotDeviceService iotDeviceService;
    
    /**
     * 查询IoT设备列表
     */
    @SaCheckPermission("iot:device:list")
    @GetMapping("/list")
    public TableDataInfo<IotDeviceVo> list(IotDeviceBo bo, PageQuery pageQuery) {
        return iotDeviceService.queryPageList(bo, pageQuery);
    }
    
    /**
     * 导出IoT设备列表
     */
    @SaCheckPermission("iot:device:export")
    @Log(title = "IoT设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IotDeviceBo bo, HttpServletResponse response) {
        List<IotDeviceVo> list = iotDeviceService.queryList(bo);
        ExcelUtil.exportExcel(list, "IoT设备", IotDeviceVo.class, response);
    }
    
    /**
     * 获取IoT设备详细信息
     */
    @SaCheckPermission("iot:device:query")
    @GetMapping("/{deviceId}")
    public R<IotDeviceVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long deviceId) {
        return R.ok(iotDeviceService.queryById(deviceId));
    }
    
    /**
     * 新增IoT设备
     */
    @SaCheckPermission("iot:device:add")
    @Log(title = "IoT设备", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody IotDeviceBo bo) {
        return toAjax(iotDeviceService.insertByBo(bo));
    }
    
    /**
     * 修改IoT设备
     */
    @SaCheckPermission("iot:device:edit")
    @Log(title = "IoT设备", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody IotDeviceBo bo) {
        return toAjax(iotDeviceService.updateByBo(bo));
    }
    
    /**
     * 删除IoT设备
     */
    @SaCheckPermission("iot:device:remove")
    @Log(title = "IoT设备", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deviceIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] deviceIds) {
        return toAjax(iotDeviceService.deleteWithValidByIds(List.of(deviceIds), true));
    }
    
    /**
     * 获取设备实时状态
     */
    @SaCheckPermission("iot:device:query")
    @GetMapping("/status/{imei}")
    public R<IotDeviceStatusVo> getDeviceStatus(@NotBlank(message = "IMEI不能为空")
                                                @PathVariable String imei) {
        return R.ok(iotDeviceService.getDeviceStatus(imei));
    }
    
    /**
     * 设备状态统计
     */
    @SaCheckPermission("iot:device:list")
    @GetMapping("/statistics")
    public R<IotDeviceStatisticsVo> getStatistics() {
        return R.ok(iotDeviceService.getDeviceStatistics());
    }
}
```

### 7.2 定位数据接口
### 7.2 定位数据接口
```java
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/location")
public class IotLocationController extends BaseController {
    
    private final IIotLocationDataService iotLocationDataService;
    
    /**
     * 查询设备定位数据列表
     */
    @SaCheckPermission("iot:location:list")
    @GetMapping("/list")
    public TableDataInfo<IotLocationDataVo> list(IotLocationDataBo bo, PageQuery pageQuery) {
        return iotLocationDataService.queryPageList(bo, pageQuery);
    }
    
    /**
     * 获取设备当前位置
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/current/{imei}")
    public R<IotLocationDataVo> getCurrentLocation(@NotBlank(message = "IMEI不能为空")
                                                   @PathVariable String imei) {
        return R.ok(iotLocationDataService.getCurrentLocation(imei));
    }
    
    /**
     * 查询设备历史轨迹
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/track/{imei}")
    public R<List<IotLocationDataVo>> getTrack(
        @NotBlank(message = "IMEI不能为空") @PathVariable String imei,
        @NotNull(message = "开始时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @NotNull(message = "结束时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return R.ok(iotLocationDataService.getTrackData(imei, startTime, endTime));
    }
    
    /**
     * 获取轨迹回放数据
     */
    @SaCheckPermission("iot:location:query")
    @GetMapping("/playback/{imei}")
    public R<List<IotLocationDataVo>> getPlaybackData(
        @NotBlank(message = "IMEI不能为空") @PathVariable String imei,
        @NotNull(message = "开始时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @NotNull(message = "结束时间不能为空") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
        @RequestParam(defaultValue = "60") Integer interval) {
        return R.ok(iotLocationDataService.getPlaybackData(imei, startTime, endTime, interval));
    }
    
    /**
     * 导出定位数据
     */
    @SaCheckPermission("iot:location:export")
    @Log(title = "定位数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(IotLocationDataBo bo, HttpServletResponse response) {
        List<IotLocationDataVo> list = iotLocationDataService.queryList(bo);
        ExcelUtil.exportExcel(list, "定位数据", IotLocationDataVo.class, response);
    }
}

### 7.3 服务层接口设计
```java
/**
 * IoT设备Service接口
 */
public interface IIotDeviceService extends IServicePlus<IotDevice, IotDeviceVo> {
    
    /**
     * 查询IoT设备列表
     */
    TableDataInfo<IotDeviceVo> queryPageList(IotDeviceBo bo, PageQuery pageQuery);
    
    /**
     * 查询IoT设备列表
     */
    List<IotDeviceVo> queryList(IotDeviceBo bo);
    
    /**
     * 根据新增业务对象插入IoT设备
     */
    Boolean insertByBo(IotDeviceBo bo);
    
    /**
     * 根据编辑业务对象修改IoT设备
     */
    Boolean updateByBo(IotDeviceBo bo);
    
    /**
     * 校验并批量删除IoT设备信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
    
    /**
     * 根据IMEI获取设备状态
     */
    IotDeviceStatusVo getDeviceStatus(String imei);
    
    /**
     * 获取设备统计信息
     */
    IotDeviceStatisticsVo getDeviceStatistics();
    
    /**
     * 设备上线处理
     */
    void handleDeviceOnline(String imei, String clientIp);
    
    /**
     * 设备离线处理
     */
    void handleDeviceOffline(String imei);
}

/**
 * IoT设备Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class IotDeviceServiceImpl extends ServicePlusImpl<IotDeviceMapper, IotDevice, IotDeviceVo> implements IIotDeviceService {
    
    private final IotDeviceMapper baseMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 查询IoT设备
     */
    @Override
    public IotDeviceVo queryById(Long deviceId){
        return getVoById(deviceId, this.buildQueryWrapper());
    }
    
    private LambdaQueryWrapper<IotDevice> buildQueryWrapper() {
        Map<String, Object> params = getParams();
        LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQueryWrapper();
        lqw.eq(StringUtils.isNotBlank(MapUtil.getStr(params, "imei")), IotDevice::getImei, MapUtil.getStr(params, "imei"));
        lqw.like(StringUtils.isNotBlank(MapUtil.getStr(params, "deviceName")), IotDevice::getDeviceName, MapUtil.getStr(params, "deviceName"));
        lqw.eq(StringUtils.isNotBlank(MapUtil.getStr(params, "deviceType")), IotDevice::getDeviceType, MapUtil.getStr(params, "deviceType"));
        lqw.eq(StringUtils.isNotBlank(MapUtil.getStr(params, "status")), IotDevice::getStatus, MapUtil.getStr(params, "status"));
        return lqw;
    }
    
    /**
     * 查询IoT设备列表
     */
    @Override
    public TableDataInfo<IotDeviceVo> queryPageList(IotDeviceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<IotDevice> lqw = buildQueryWrapper(bo);
        Page<IotDeviceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }
    
    /**
     * 查询IoT设备列表
     */
    @Override
    public List<IotDeviceVo> queryList(IotDeviceBo bo) {
        LambdaQueryWrapper<IotDevice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }
    
    private LambdaQueryWrapper<IotDevice> buildQueryWrapper(IotDeviceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQueryWrapper();
        lqw.eq(StringUtils.isNotBlank(bo.getImei()), IotDevice::getImei, bo.getImei());
        lqw.like(StringUtils.isNotBlank(bo.getDeviceName()), IotDevice::getDeviceName, bo.getDeviceName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeviceType()), IotDevice::getDeviceType, bo.getDeviceType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), IotDevice::getStatus, bo.getStatus());
        lqw.between(params.get("beginRegisterTime") != null && params.get("endRegisterTime") != null,
            IotDevice::getRegisterTime, params.get("beginRegisterTime"), params.get("endRegisterTime"));
        return lqw;
    }
    
    /**
     * 新增IoT设备
     */
    @Override
    public Boolean insertByBo(IotDeviceBo bo) {
        IotDevice add = MapstructUtils.convert(bo, IotDevice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeviceId(add.getDeviceId());
        }
        return flag;
    }
    
    /**
     * 修改IoT设备
     */
    @Override
    public Boolean updateByBo(IotDeviceBo bo) {
        IotDevice update = MapstructUtils.convert(bo, IotDevice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }
    
    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(IotDevice entity){
        // 校验IMEI唯一性
        if (StringUtils.isNotEmpty(entity.getImei())) {
            LambdaQueryWrapper<IotDevice> lqw = Wrappers.lambdaQueryWrapper();
            lqw.eq(IotDevice::getImei, entity.getImei());
            lqw.ne(entity.getDeviceId() != null, IotDevice::getDeviceId, entity.getDeviceId());
            long count = baseMapper.selectCount(lqw);
            if (count > 0) {
                throw new ServiceException("IMEI号已存在");
            }
        }
    }
    
    /**
     * 批量删除IoT设备
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
    
    @Override
    public IotDeviceStatusVo getDeviceStatus(String imei) {
        // 从Redis缓存获取设备实时状态
        String cacheKey = CacheNames.IOT_DEVICE_STATUS + imei;
        IotDeviceStatusVo status = (IotDeviceStatusVo) redisTemplate.opsForValue().get(cacheKey);
        if (status == null) {
            // 缓存不存在时从数据库查询
            status = baseMapper.selectDeviceStatusByImei(imei);
        }
        return status;
    }
    
    @Override
    public IotDeviceStatisticsVo getDeviceStatistics() {
        return baseMapper.selectDeviceStatistics();
    }
    
    @Override
    public void handleDeviceOnline(String imei, String clientIp) {
        // 更新设备在线状态
        LambdaUpdateWrapper<IotDevice> updateWrapper = Wrappers.lambdaUpdateWrapper();
        updateWrapper.eq(IotDevice::getImei, imei)
                    .set(IotDevice::getStatus, "1")
                    .set(IotDevice::getLastOnlineTime, new Date());
        baseMapper.update(null, updateWrapper);
        
        // 缓存设备状态
        String cacheKey = CacheNames.IOT_DEVICE_STATUS + imei;
        IotDeviceStatusVo status = new IotDeviceStatusVo();
        status.setImei(imei);
        status.setStatus("1");
        status.setLastOnlineTime(new Date());
        status.setClientIp(clientIp);
        redisTemplate.opsForValue().set(cacheKey, status, Duration.ofMinutes(10));
    }
    
    @Override
    public void handleDeviceOffline(String imei) {
        // 更新设备离线状态
        LambdaUpdateWrapper<IotDevice> updateWrapper = Wrappers.lambdaUpdateWrapper();
        updateWrapper.eq(IotDevice::getImei, imei)
                    .set(IotDevice::getStatus, "0");
        baseMapper.update(null, updateWrapper);
        
        // 删除缓存
        String cacheKey = CacheNames.IOT_DEVICE_STATUS + imei;
        redisTemplate.delete(cacheKey);
    }
}
```

## 8. 实时数据推送设计

### 8.1 WebSocket推送
```java
@Component
public class IotWebSocketHandler {
    
    /**
     * 推送设备实时位置
     */
    public void pushLocationData(String imei, IotLocationVo location);
    
    /**
     * 推送设备状态变更
     */
    public void pushDeviceStatus(String imei, IotDeviceStatusVo status);
    
    /**
     * 推送报警信息
     */
    public void pushAlarmData(String imei, IotAlarmVo alarm);
}
```

### 8.2 SSE推送
```java
@RestController
@RequestMapping("/iot/sse")
public class IotSseController {
    
    /**
     * 设备状态SSE推送
     */
    @GetMapping("/device-status")
    public SseEmitter deviceStatusStream();
    
    /**
     * 实时位置SSE推送
     */
    @GetMapping("/location/{imei}")
    public SseEmitter locationStream(@PathVariable String imei);
}
```

## 9. 监控和运维设计

### 9.1 系统监控指标
- TCP连接数统计
- 消息处理速度(TPS)
- 设备在线率统计
- 数据包错误率
- 内存和CPU使用率

### 9.2 告警机制
- 设备长时间离线告警
- 消息处理异常告警
- 系统资源使用率告警
- 数据库连接异常告警

## 10. 部署和配置

### 10.1 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 7.0+
- 内存: 最低4GB，推荐8GB+
- 网络: 支持TCP 8888端口

### 10.2 性能调优建议
- Netty线程池大小根据CPU核数调整
- 数据库连接池配置优化
- Redis缓存过期策略配置
- JVM堆内存和GC参数调优

## 11. 错误处理和异常设计

### 11.1 协议层异常处理
```java
public enum IotErrorCode {
    // 协议解析错误
    PROTOCOL_PARSE_ERROR(10001, "协议解析失败"),
    CRC_CHECK_FAILED(10002, "CRC校验失败"),
    INVALID_PACKET_FORMAT(10003, "数据包格式错误"),
    UNSUPPORTED_PROTOCOL(10004, "不支持的协议类型"),
    
    // 设备认证错误
    DEVICE_NOT_REGISTERED(20001, "设备未注册"),
    DEVICE_AUTHENTICATION_FAILED(20002, "设备认证失败"),
    DEVICE_DISABLED(20003, "设备已禁用"),
    
    // 业务处理错误
    LOCATION_DATA_INVALID(30001, "定位数据无效"),
    ALARM_PROCESSING_FAILED(30002, "报警处理失败"),
    COMMAND_EXECUTION_FAILED(30003, "指令执行失败"),
    
    // 系统错误
    DATABASE_ERROR(40001, "数据库操作失败"),
    CACHE_ERROR(40002, "缓存操作失败"),
    NETWORK_ERROR(40003, "网络连接异常");
    
    private final int code;
    private final String message;
}
```

### 11.2 异常处理策略
```java
@Component
public class IotExceptionHandler {
    
    /**
     * 协议解析异常处理
     */
    public void handleProtocolException(ChannelHandlerContext ctx, IotException e) {
        // 记录错误日志
        // 发送错误响应
        // 根据错误类型决定是否关闭连接
    }
    
    /**
     * 业务处理异常处理
     */
    public void handleBusinessException(String imei, IotException e) {
        // 记录业务异常
        // 发送告警通知
        // 更新设备状态
    }
}
```

## 12. 安全性设计

### 12.1 设备认证机制
```java
@Service
public class IotDeviceAuthService {
    
    /**
     * IMEI白名单验证
     */
    public boolean validateImeiWhitelist(String imei);
    
    /**
     * 设备证书验证（可选）
     */
    public boolean validateDeviceCertificate(String imei, byte[] certificate);
    
    /**
     * IP地址限制
     */
    public boolean validateIpAddress(String clientIp, String imei);
    
    /**
     * 频率限制
     */
    public boolean checkRateLimit(String imei, String operation);
}
```

### 12.2 数据安全
- 敏感数据加密存储（IMEI、IMSI等）
- 通信数据完整性校验（CRC-ITU）
- 访问日志记录和审计
- 数据脱敏处理

### 12.3 网络安全
```yaml
# 安全配置
iot:
  security:
    # IP白名单
    ip-whitelist:
      enabled: true
      allowed-ips:
        - "***********/24"
        - "10.0.0.0/8"
    
    # 连接限制
    connection-limit:
      max-connections-per-ip: 100
      max-total-connections: 10000
    
    # 频率限制
    rate-limit:
      enabled: true
      requests-per-minute: 1000
      
    # SSL/TLS支持（可选）
    ssl:
      enabled: false
      keystore-path: "classpath:ssl/server.jks"
      keystore-password: "password"
```

## 13. 性能优化设计

### 13.1 连接池优化
```java
@Configuration
public class IotNettyConfig {
    
    @Bean
    public EventLoopGroup bossGroup() {
        return new NioEventLoopGroup(1, new DefaultThreadFactory("iot-boss"));
    }
    
    @Bean
    public EventLoopGroup workerGroup() {
        int workerThreads = Math.max(1, SystemPropertyUtil.getInt(
            "io.netty.eventLoopThreads", NettyRuntime.availableProcessors() * 2));
        return new NioEventLoopGroup(workerThreads, new DefaultThreadFactory("iot-worker"));
    }
}
```

### 13.2 数据库优化
```sql
-- 分区表设计（按月分区）
CREATE TABLE iot_location_data_202412 PARTITION OF iot_location_data
FOR VALUES FROM ('2024-12-01') TO ('2025-01-01');

-- 索引优化
CREATE INDEX CONCURRENTLY idx_location_device_time_covering 
ON iot_location_data (device_id, gps_time) 
INCLUDE (latitude, longitude, speed, direction);

-- 历史数据归档策略
CREATE OR REPLACE FUNCTION archive_old_location_data()
RETURNS void AS $$
BEGIN
    -- 归档6个月前的数据到历史表
    INSERT INTO iot_location_data_archive 
    SELECT * FROM iot_location_data 
    WHERE gps_time < NOW() - INTERVAL '6 months';
    
    -- 删除已归档的数据
    DELETE FROM iot_location_data 
    WHERE gps_time < NOW() - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;
```

### 13.3 缓存策略
```java
@Service
public class IotCacheService {
    
    /**
     * 设备状态缓存（Redis Hash）
     * Key: device:status:{imei}
     * TTL: 5分钟
     */
    public void cacheDeviceStatus(String imei, IotDeviceStatus status);
    
    /**
     * 最新位置缓存（Redis String）
     * Key: device:location:{imei}
     * TTL: 10分钟
     */
    public void cacheLatestLocation(String imei, IotLocationData location);
    
    /**
     * 设备连接信息缓存（Redis Set）
     * Key: device:online
     * TTL: 心跳超时时间
     */
    public void cacheOnlineDevices(Set<String> imeiSet);
}
```

## 14. 扩展性设计

### 14.1 协议扩展机制
```java
public interface IotProtocolHandler {
    
    /**
     * 支持的协议号
     */
    byte getSupportedProtocol();
    
    /**
     * 处理协议消息
     */
    IotMessage handleMessage(byte[] data, ChannelHandlerContext ctx);
    
    /**
     * 构建响应消息
     */
    byte[] buildResponse(IotMessage request);
}

@Component
public class IotProtocolRegistry {
    
    private final Map<Byte, IotProtocolHandler> handlers = new ConcurrentHashMap<>();
    
    /**
     * 注册协议处理器
     */
    public void registerHandler(IotProtocolHandler handler) {
        handlers.put(handler.getSupportedProtocol(), handler);
    }
    
    /**
     * 获取协议处理器
     */
    public IotProtocolHandler getHandler(byte protocol) {
        return handlers.get(protocol);
    }
}
```

### 14.2 多设备类型支持
```java
public enum DeviceType {
    GT06("GT06", "GT06定位器"),
    GT02("GT02", "GT02定位器"),
    GT300("GT300", "GT300定位器"),
    CUSTOM("CUSTOM", "自定义设备");
    
    private final String code;
    private final String name;
}

@Service
public class DeviceTypeService {
    
    /**
     * 根据设备类型获取协议处理器
     */
    public IotProtocolHandler getProtocolHandler(DeviceType deviceType);
    
    /**
     * 设备类型自动识别
     */
    public DeviceType detectDeviceType(String imei, byte[] firstPacket);
}
```

### 14.3 插件化架构
```java
public interface IotPlugin {
    
    /**
     * 插件名称
     */
    String getName();
    
    /**
     * 插件版本
     */
    String getVersion();
    
    /**
     * 插件初始化
     */
    void initialize(IotPluginContext context);
    
    /**
     * 处理设备消息
     */
    void handleMessage(IotMessage message);
    
    /**
     * 插件销毁
     */
    void destroy();
}
```

## 15. 测试策略

### 15.1 单元测试
```java
@SpringBootTest
class IotProtocolDecoderTest {
    
    @Test
    void testLoginPacketDecode() {
        // 测试登录包解析
        byte[] loginPacket = {0x78, 0x78, 0x0D, 0x01, ...};
        IotMessage message = decoder.decode(loginPacket);
        assertThat(message.getProtocol()).isEqualTo((byte) 0x01);
    }
    
    @Test
    void testCrcValidation() {
        // 测试CRC校验
        byte[] invalidPacket = {0x78, 0x78, 0x0D, 0x01, ...};
        assertThrows(CrcValidationException.class, 
            () -> decoder.decode(invalidPacket));
    }
}
```

### 15.2 集成测试
```java
@SpringBootTest
@TestPropertySource(properties = {
    "iot.tcp-server.port=18888",
    "iot.tcp-server.enabled=true"
})
class IotTcpServerIntegrationTest {
    
    @Test
    void testDeviceConnection() {
        // 模拟设备连接
        // 发送登录包
        // 验证响应
        // 发送定位包
        // 验证数据存储
    }
}
```

### 15.3 性能测试
```java
@Component
public class IotPerformanceTest {
    
    /**
     * 并发连接测试
     */
    public void testConcurrentConnections(int connectionCount);
    
    /**
     * 消息处理性能测试
     */
    public void testMessageThroughput(int messagesPerSecond);
    
    /**
     * 内存使用测试
     */
    public void testMemoryUsage(int deviceCount, int duration);
}
```

## 16. 运维和监控

### 16.1 健康检查
```java
@Component
public class IotHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        Health.Builder builder = new Health.Builder();
        
        // 检查TCP服务器状态
        if (tcpServer.isRunning()) {
            builder.up();
        } else {
            builder.down().withDetail("tcp-server", "not running");
        }
        
        // 检查数据库连接
        // 检查Redis连接
        // 检查在线设备数量
        
        return builder.build();
    }
}
```

### 16.2 指标收集
```java
@Component
public class IotMetricsCollector {
    
    private final MeterRegistry meterRegistry;
    
    /**
     * 连接数指标
     */
    @EventListener
    public void onDeviceConnected(DeviceConnectedEvent event) {
        meterRegistry.counter("iot.device.connected").increment();
    }
    
    /**
     * 消息处理指标
     */
    public void recordMessageProcessed(String protocol, long processingTime) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("iot.message.processing.time")
            .tag("protocol", protocol)
            .register(meterRegistry));
    }
}
```

### 16.3 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <!-- IoT模块专用日志 -->
    <appender name="IOT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/iot.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/iot.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.yunqu.park.iot" level="INFO" additivity="false">
        <appender-ref ref="IOT_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
</configuration>
```

## 17. 部署方案

### 17.1 Docker部署
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app

COPY park-admin/target/park-admin.jar app.jar

EXPOSE 8080 8888

ENV JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC"

CMD ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

### 17.2 Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: park-iot-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: park-iot-server
  template:
    metadata:
      labels:
        app: park-iot-server
    spec:
      containers:
      - name: park-iot-server
        image: park-iot:latest
        ports:
        - containerPort: 8080
        - containerPort: 8888
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
---
apiVersion: v1
kind: Service
metadata:
  name: park-iot-service
spec:
  selector:
    app: park-iot-server
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: tcp
    port: 8888
    targetPort: 8888
  type: LoadBalancer
```

---

**文档版本**: v1.0  
**创建时间**: 2024-12-19  
**更新时间**: 2024-12-19  
**作者**: CodeBuddy AI Assistant

**设计文档完成说明**：
本设计文档涵盖了GT06物联网设备协议对接的完整技术方案，包括：
- 详细的协议分析和系统架构设计
- 完整的数据库设计和API接口规范
- 全面的错误处理和安全性考虑
- 性能优化和扩展性设计
- 完整的测试策略和运维方案
- 容器化部署和监控配置

该设计方案符合当前项目的技术栈和开发规范，可直接用于指导开发实施。
