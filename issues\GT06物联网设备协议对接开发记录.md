# GT06物联网设备协议对接开发记录

## 开发概述
基于GT06物联网设备协议对接详细设计文档，完成IoT设备管理模块的开发工作。

## 已完成的开发任务

### 1. 模块基础架构搭建 ✅
- [x] 创建park-iot模块目录结构
- [x] 配置pom.xml，添加Netty等必要依赖
- [x] 更新父模块pom.xml，注册新模块
- [x] 添加Netty版本管理到主pom.xml

### 2. 数据库设计 ✅
- [x] 创建SQL初始化脚本：`script/sql/iot_tables.sql`
- [x] 包含4个核心表：
  - iot_device (设备基础信息表)
  - iot_location_data (设备定位数据表)
  - iot_alarm_record (设备报警记录表)
  - iot_device_status_log (设备状态日志表)

### 3. 核心实体类 ✅
- [x] IotDevice.java (设备基础信息实体)
- [x] IotLocationData.java (定位数据实体)
- [x] IotAlarmRecord.java (报警记录实体)
- [x] IotDeviceBo.java (业务对象)
- [x] IotDeviceVo.java (视图对象)
- [x] IotLocationDataBo.java (定位数据业务对象)
- [x] IotLocationDataVo.java (定位数据视图对象)
- [x] IotAlarmRecordBo.java (报警记录业务对象)
- [x] IotAlarmRecordVo.java (报警记录视图对象)

### 4. 工具类和常量 ✅
- [x] IotConstants.java (IoT模块常量定义)
- [x] CrcUtils.java (CRC-ITU校验工具类)

### 5. Netty网络层 ✅
- [x] IotMessage.java (IoT协议消息对象)
- [x] GT06ProtocolDecoder.java (协议解码器)
- [x] GT06ProtocolEncoder.java (协议编码器)
- [x] IotMessageHandler.java (消息处理器)
- [x] IotChannelInitializer.java (通道初始化器)
- [x] IotTcpServer.java (TCP服务器)
- [x] DeviceConnectionManager.java (设备连接管理器)
- [x] CommandResponseHandler.java (指令响应处理器)

### 6. 业务服务层 ✅
- [x] IIotProtocolService.java (协议处理服务接口)
- [x] IotProtocolServiceImpl.java (协议处理服务实现，包含数据解析逻辑)
- [x] IIotDeviceService.java (设备管理服务接口)
- [x] IotDeviceServiceImpl.java (设备管理服务实现)
- [x] IIotLocationDataService.java (定位数据服务接口)
- [x] IotLocationDataServiceImpl.java (定位数据服务实现)
- [x] IIotAlarmRecordService.java (报警记录服务接口)
- [x] IotAlarmRecordServiceImpl.java (报警记录服务实现)
- [x] IIotCommandService.java (设备指令服务接口)
- [x] IotCommandServiceImpl.java (设备指令服务实现)

### 9. 配置管理层 ✅
- [x] IotLogConfig.java (日志配置管理)
- [x] IotLogMarkers.java (日志标记常量)
- [x] logback-iot-config.xml (模块独立日志配置)

### 7. 数据访问层 ✅
- [x] IotDeviceMapper.java (设备Mapper接口)
- [x] IotLocationDataMapper.java (定位数据Mapper接口)
- [x] IotAlarmRecordMapper.java (报警记录Mapper接口)
- [x] IotDeviceMapper.xml (设备MyBatis映射文件)
- [x] IotLocationDataMapper.xml (定位数据MyBatis映射文件)
- [x] IotAlarmRecordMapper.xml (报警记录MyBatis映射文件)

### 8. 控制器层 ✅
- [x] IotDeviceController.java (设备管理控制器)
- [x] IotLocationController.java (定位数据控制器)
- [x] IotAlarmController.java (报警记录控制器)
- [x] IotCommandController.java (设备指令控制器)
- [x] IotMonitorController.java (IoT监控控制器)
- [x] LogTestController.java (日志测试控制器)

### 9. 配置文件 ✅
- [x] application-iot.yml (IoT模块配置)
- [x] 更新application-dev.yml包含IoT配置
- [x] 更新park-admin/pom.xml包含IoT模块依赖

## 技术实现要点

### 协议解析
- 支持GT06协议的0x7878和0x7979两种起始位
- 实现CRC-ITU校验算法
- 支持多种协议类型：登录、定位、心跳、报警等

### 网络通信
- 基于Netty 4.x实现TCP服务器
- 支持设备连接管理和心跳监控
- 实现协议编解码和消息路由

### 数据存储
- 使用MyBatis-Plus进行数据访问
- 支持多租户架构
- 实现Redis缓存优化

### 系统集成
- 遵循项目现有架构规范
- 集成Spring Security权限控制
- 支持Excel导入导出功能

## 下一步开发计划

### 待完成功能
1. **定位数据处理**
   - 完善定位数据解析逻辑
   - 实现定位数据存储和查询
   - 添加轨迹回放功能

2. **报警处理**
   - 完善报警数据解析
   - 实现报警推送机制
   - 添加报警处理工作流

3. **实时推送**
   - 集成WebSocket推送
   - 实现SSE事件流
   - 添加实时监控界面

4. **系统监控**
   - 添加设备连接统计
   - 实现性能监控指标
   - 添加健康检查接口

5. **测试和优化**
   - 编写单元测试
   - 进行性能测试
   - 优化数据库查询

## 部署说明

### 数据库初始化
执行SQL脚本：`script/sql/iot_tables.sql`

### 配置说明
- TCP服务器端口：8888
- 心跳超时：300秒
- 支持的设备类型：GT06

### 启动验证
1. 检查TCP服务器是否正常启动
2. 验证设备连接和登录功能
3. 测试协议解析和响应

## 开发规范遵循
- 遵循项目代码规范和命名约定
- 使用统一的异常处理机制
- 实现完整的日志记录
- 支持多租户数据隔离

## 技术栈版本
- Spring Boot: 3.4.4
- JDK: 17
- Netty: 4.1.115.Final
- MyBatis-Plus: 3.5.11
- MySQL: 8.0+
- Redis: 7.x

## 新增功能特性

### 设备指令管理：
- ✅ 支持多种标准指令（重启、复位、配置等）
- ✅ 自定义指令支持
- ✅ 批量指令发送
- ✅ 指令发送历史记录
- ✅ 设备在线状态验证
- ✅ 直接通过Netty连接发送指令
- ✅ 指令响应处理和缓存

### 连接管理：
- ✅ 设备连接状态实时管理
- ✅ 通道与设备IMEI映射
- ✅ 在线设备统计
- ✅ 连接断开自动清理

### 指令响应处理：
- ✅ 终端指令响应解析
- ✅ 响应状态码映射
- ✅ 响应结果缓存
- ✅ 响应历史查询

### 日志监控系统：
- ✅ 分类日志输出（连接、协议、指令、服务器）
- ✅ 结构化日志格式（带标签和状态图标）
- ✅ 设备动态追踪（连接、断开、消息、指令）
- ✅ 统计计数器（连接数、消息数、指令数、错误数）
- ✅ 性能监控日志
- ✅ 原始数据调试日志
- ✅ 日志文件分类存储
- ✅ 集成到主应用日志配置
- ✅ 日志测试接口
- ✅ 监控统计接口
- ✅ 模块化日志配置管理
- ✅ SLF4J标记分类系统
- ✅ 自动配置检查和验证

## 全局Date类型转换问题修复 ✅

### 问题描述：
前端传递空字符串给Date类型字段时出现类型转换异常：
```
Failed to convert property value of type 'java.lang.String' to required type 'java.util.Date' for property 'createTime'
```

### 解决方案：
在`park-common-web`模块中创建全局Date类型转换配置：

### 核心组件：
- ✅ `GlobalDateConverterConfig.java` - 全局Date转换器
- ✅ `GlobalWebMvcConfig.java` - Web MVC配置注册
- ✅ `GlobalJacksonConfig.java` - Jackson JSON反序列化配置
- ✅ `GlobalTypeConversionExceptionHandler.java` - 全局异常处理
- ✅ `GlobalDateConversionAutoConfiguration.java` - 自动配置类

### 支持功能：
- ✅ 空字符串转换为null（`""`, `" "`, `"null"`, `"undefined"`）
- ✅ 时间戳转换（毫秒/秒级）
- ✅ 多种日期格式智能解析
- ✅ 支持java.util.Date、java.sql.Date、java.sql.Timestamp
- ✅ GET请求参数和POST请求体转换
- ✅ 友好的错误提示信息
- ✅ 测试接口验证功能

## GT06协议解码器安全性修复 ✅

### 问题描述：
解码器出现ByteBuf越界异常：
```
readerIndex(3) + length(16) exceeds writerIndex(18): PooledUnsafeDirectByteBuf(ridx: 3, widx: 18, cap: 2048)
```

### 问题原因：
- 解码器在读取数据时没有充分验证缓冲区边界
- 缺少安全的字节读取机制
- 数据包长度计算错误导致读取超出范围

### 解决方案：
1. **创建安全工具类** (`ByteBufUtils.java`)：
   - ✅ 安全的字节读取方法
   - ✅ 缓冲区边界检查
   - ✅ 数据完整性验证
   - ✅ 调试辅助方法

2. **修复解码器** (`GT06ProtocolDecoder.java`)：
   - ✅ 使用安全的字节读取方法
   - ✅ 增强数据完整性检查
   - ✅ 改进错误处理和日志输出
   - ✅ 详细的调试信息输出

3. **协议解码测试** (`ProtocolDecodeTestController.java`)：
   - ✅ ByteBuf安全操作测试
   - ✅ 数据包结构分析
   - ✅ 解码过程模拟
   - ✅ 数据完整性检查

### 核心改进：
- ✅ 所有字节读取操作都进行边界检查
- ✅ 详细的日志输出便于问题定位
- ✅ 原始数据十六进制输出用于调试
- ✅ 分步骤的解码过程验证
- ✅ 完整的错误恢复机制

## CRC校验问题修复 ✅

### 问题描述：
CRC校验失败导致数据包解析中断：
```
[PROTOCOL-DECODE] ❌ CRC validation failed: RemoteAddress=/**************:29177, PacketLength=13
[PROTOCOL-DECODE] ❌ Failed to decode message: Error=CRC validation failed
```

### 问题原因：
- CRC算法字节序问题（大端序 vs 小端序）
- 数据范围选择错误
- 缺少宽松的校验模式

### 解决方案：
1. **增强CRC工具类** (`CrcUtils.java`)：
   - ✅ 修复字节序问题（GT06使用小端序）
   - ✅ 添加宽松校验模式（尝试多种字节序）
   - ✅ 详细的调试日志输出
   - ✅ 支持不同数据范围的校验

2. **协议配置管理** (`IotProtocolConfig.java`)：
   - ✅ 可配置的CRC校验策略
   - ✅ 开发/生产环境不同配置
   - ✅ 校验失败时的处理策略

3. **解码器优化** (`GT06ProtocolDecoder.java`)：
   - ✅ 使用配置化的CRC校验
   - ✅ 支持跳过CRC校验继续处理
   - ✅ 详细的错误日志和调试信息

4. **CRC测试工具** (`CrcTestController.java`)：
   - ✅ CRC计算和校验测试
   - ✅ GT06数据包CRC分析
   - ✅ 测试数据包生成
   - ✅ 多种字节序测试

### 核心改进：
- ✅ 支持小端序和大端序CRC校验
- ✅ 宽松模式自动尝试多种校验方式
- ✅ 可配置的校验策略和错误处理
- ✅ 完整的CRC测试和调试工具
- ✅ 详细的日志输出便于问题定位
