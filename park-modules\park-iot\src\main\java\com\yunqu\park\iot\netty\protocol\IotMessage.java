package com.yunqu.park.iot.netty.protocol;

import lombok.Data;

/**
 * IoT协议消息对象
 *
 * <AUTHOR>
 */
@Data
public class IotMessage {

    /**
     * 起始位
     */
    private byte[] startFlag;

    /**
     * 包长度
     */
    private int packetLength;

    /**
     * 协议号
     */
    private byte protocol;

    /**
     * 消息内容
     */
    private byte[] content;

    /**
     * 信息序列号
     */
    private int sequenceNumber;

    /**
     * CRC校验值
     */
    private int crc;

    /**
     * 停止位
     */
    private byte[] stopFlag;

    /**
     * 设备IMEI(从消息内容中解析)
     */
    private String imei;

    /**
     * 消息接收时间
     */
    private long receiveTime;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 客户端端口
     */
    private int clientPort;

    public IotMessage() {
        this.receiveTime = System.currentTimeMillis();
    }

    /**
     * 判断是否需要响应
     * @return true-需要响应, false-不需要响应
     */
    public boolean needResponse() {
        return protocol == 0x01 ||  // 登录包
               protocol == 0x13 ||  // 心跳包
               protocol == 0x16 ||  // 报警包
               protocol == (byte) 0x8D; // 录音协议包
    }

    /**
     * 判断是否使用0x7979起始位
     * @return true-使用0x7979, false-使用0x7878
     */
    public boolean use7979StartFlag() {
        return protocol == (byte) 0x94 || protocol == (byte) 0x8D;
    }

    /**
     * 获取协议名称
     * 修正：按照concox-master标准协议号映射
     * @return 协议名称
     */
    public String getProtocolName() {
        return switch (protocol & 0xFF) {
            case 0x01 -> "登录包";
            case 0x11 -> "LBS信息包";
            case 0x12 -> "定位数据包(自定义)";
            case 0x13 -> "状态信息包";
            case 0x15 -> "终端指令响应";
            case 0x16 -> "组合信息包(GPS+LBS+Status)";
            case 0x17 -> "LBS电话号码查询定位";
            case 0x18 -> "LBS多基站信息(自定义)";
            case 0x19 -> "LBS状态信息";
            case 0x1A -> "GPS电话号码查询定位";
            case 0x23 -> "心跳包";
            case 0x26 -> "报警包";
            case 0x2C -> "LBS+WIFI信息";
            case 0x80 -> "服务器指令下发";
            case 0x8A -> "时间校验包";
            case 0x8D -> "录音协议包";
            case 0x90 -> "IMSI号上报";
            case 0x94 -> "ICCID号上报";
            case 0x21 -> "在线指令";
            default -> "未知协议(" + String.format("0x%02X", protocol & 0xFF) + ")";
        };
    }

    /**
     * 阶段2优化：重置对象状态，用于对象池复用
     */
    public void reset() {
        this.startFlag = null;
        this.packetLength = 0;
        this.protocol = 0;
        this.content = null;
        this.sequenceNumber = 0;
        this.crc = 0;
        this.stopFlag = null;
        this.imei = null;
        this.clientIp = null;
        this.clientPort = 0;
        this.receiveTime = System.currentTimeMillis();
    }

    @Override
    public String toString() {
        return String.format("IotMessage{protocol=0x%02X(%s), imei='%s', sequenceNumber=%d, contentLength=%d}",
                protocol & 0xFF, getProtocolName(), imei, sequenceNumber,
                content != null ? content.length : 0);
    }
}
