package com.yunqu.park.iot.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.controller.IotCommandController.BatchCommandRequest;
import com.yunqu.park.iot.service.IIotCommandService;
import com.yunqu.park.iot.service.IIotDeviceService;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

/**
 * IoT设备指令服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotCommandServiceImpl implements IIotCommandService {

    private final IIotDeviceService deviceService;
    private final DeviceConnectionManager connectionManager;

    // 指令缓存键前缀
    private static final String COMMAND_CACHE_PREFIX = "iot:command:";
    private static final String COMMAND_HISTORY_PREFIX = "iot:command:history:";

    @Override
    public Boolean sendRestartCommand(String imei) {
        try {
            log.info("[COMMAND-RESTART] 🔄 Processing restart command: IMEI={}", imei);

            // 检查设备是否在线
            if (!isDeviceOnline(imei)) {
                log.warn("[COMMAND-RESTART] ❌ Device offline, cannot send restart command: IMEI={}", imei);
                return false;
            }

            // 构建重启指令 (根据GT06协议规范)
            String command = buildRestartCommand();
            log.debug("[COMMAND-RESTART] Command built: IMEI={}, Command={}", imei, command);

            // 发送指令到设备
            boolean result = sendCommandToDevice(imei, "RESTART", command);

            if (result) {
                // 记录指令历史
                recordCommandHistory(imei, "RESTART", command, "设备重启指令");
                log.info("[COMMAND-RESTART] ✅ Restart command sent successfully: IMEI={}", imei);
            } else {
                log.error("[COMMAND-RESTART] ❌ Failed to send restart command: IMEI={}", imei);
            }

            return result;
        } catch (Exception e) {
            log.error("[COMMAND-RESTART] ❌ Exception sending restart command: IMEI={}, Error={}",
                     imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean sendResetCommand(String imei) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot send reset command", imei);
                return false;
            }

            String command = buildResetCommand();
            boolean result = sendCommandToDevice(imei, "RESET", command);
            
            if (result) {
                recordCommandHistory(imei, "RESET", command, "设备复位指令");
                log.info("Reset command sent to device: {}", imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to send reset command to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setReportInterval(String imei, Integer interval) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot set report interval", imei);
                return false;
            }

            String command = buildIntervalCommand(interval);
            boolean result = sendCommandToDevice(imei, "INTERVAL", command);
            
            if (result) {
                recordCommandHistory(imei, "INTERVAL", command, "设置上报间隔: " + interval + "秒");
                log.info("Report interval set to {} seconds for device: {}", interval, imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to set report interval for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setApnConfig(String imei, String apn, String username, String password) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot set APN config", imei);
                return false;
            }

            String command = buildApnCommand(apn, username, password);
            boolean result = sendCommandToDevice(imei, "APN", command);
            
            if (result) {
                recordCommandHistory(imei, "APN", command, "设置APN: " + apn);
                log.info("APN config set for device: {}", imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to set APN config for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setServerAddress(String imei, String serverIp, Integer port) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot set server address", imei);
                return false;
            }

            String command = buildServerCommand(serverIp, port);
            boolean result = sendCommandToDevice(imei, "SERVER", command);
            
            if (result) {
                recordCommandHistory(imei, "SERVER", command, "设置服务器: " + serverIp + ":" + port);
                log.info("Server address set to {}:{} for device: {}", serverIp, port, imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to set server address for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean queryDeviceStatus(String imei) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot query status", imei);
                return false;
            }

            String command = buildStatusQueryCommand();
            boolean result = sendCommandToDevice(imei, "STATUS", command);
            
            if (result) {
                recordCommandHistory(imei, "STATUS", command, "查询设备状态");
                log.info("Status query sent to device: {}", imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to query status for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setTimezone(String imei, Integer timezone) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot set timezone", imei);
                return false;
            }

            String command = buildTimezoneCommand(timezone);
            boolean result = sendCommandToDevice(imei, "TIMEZONE", command);
            
            if (result) {
                recordCommandHistory(imei, "TIMEZONE", command, "设置时区: UTC" + (timezone >= 0 ? "+" : "") + timezone);
                log.info("Timezone set to UTC{}{} for device: {}", timezone >= 0 ? "+" : "", timezone, imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to set timezone for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean setWorkMode(String imei, Integer mode) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot set work mode", imei);
                return false;
            }

            String command = buildWorkModeCommand(mode);
            boolean result = sendCommandToDevice(imei, "MODE", command);
            
            if (result) {
                recordCommandHistory(imei, "MODE", command, "设置工作模式: " + mode);
                log.info("Work mode set to {} for device: {}", mode, imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to set work mode for device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean sendCustomCommand(String imei, String command) {
        try {
            if (!isDeviceOnline(imei)) {
                log.warn("Device {} is offline, cannot send custom command", imei);
                return false;
            }

            boolean result = sendCommandToDevice(imei, "CUSTOM", command);
            
            if (result) {
                recordCommandHistory(imei, "CUSTOM", command, "自定义指令: " + command);
                log.info("Custom command sent to device: {}", imei);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Failed to send custom command to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Object getCommandHistory(String imei, Integer limit) {
        try {
            String cacheKey = COMMAND_HISTORY_PREFIX + imei;
            List<Object> history = RedisUtils.getCacheList(cacheKey);
            
            if (history == null || history.isEmpty()) {
                return new ArrayList<>();
            }
            
            // 限制返回数量
            int size = Math.min(limit, history.size());
            return history.subList(0, size);
            
        } catch (Exception e) {
            log.error("Failed to get command history for device {}: {}", imei, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Object sendBatchCommand(BatchCommandRequest request) {
        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        
        try {
            String[] imeis = request.getImeis();
            String commandType = request.getCommandType();
            
            for (String imei : imeis) {
                boolean success = false;
                
                switch (commandType.toUpperCase()) {
                    case "RESTART":
                        success = sendRestartCommand(imei);
                        break;
                    case "RESET":
                        success = sendResetCommand(imei);
                        break;
                    case "STATUS":
                        success = queryDeviceStatus(imei);
                        break;
                    default:
                        log.warn("Unsupported batch command type: {}", commandType);
                }
                
                if (success) {
                    successList.add(imei);
                } else {
                    failList.add(imei);
                }
            }
            
            result.put("total", imeis.length);
            result.put("success", successList.size());
            result.put("fail", failList.size());
            result.put("successList", successList);
            result.put("failList", failList);
            
        } catch (Exception e) {
            log.error("Failed to send batch command: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 检查设备是否在线
     */
    private boolean isDeviceOnline(String imei) {
        // 优先使用连接管理器检查设备是否在线
        return connectionManager.isDeviceOnline(imei);
    }

    /**
     * 发送指令到设备
     */
    private boolean sendCommandToDevice(String imei, String commandType, String command) {
        try {
            // 直接通过连接管理器发送指令到设备
            boolean result = connectionManager.sendCommandToDevice(imei, command);

            if (result) {
                // 将指令信息缓存用于历史记录
                String cacheKey = COMMAND_CACHE_PREFIX + imei;
                Map<String, Object> commandData = new HashMap<>();
                commandData.put("type", commandType);
                commandData.put("command", command);
                commandData.put("timestamp", System.currentTimeMillis());
                commandData.put("status", "sent");

                RedisUtils.setCacheObject(cacheKey, commandData, Duration.ofMinutes(5));
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to send command to device {}: {}", imei, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 记录指令历史
     */
    private void recordCommandHistory(String imei, String type, String command, String description) {
        try {
            String cacheKey = COMMAND_HISTORY_PREFIX + imei;

            Map<String, Object> record = new HashMap<>();
            record.put("type", type);
            record.put("command", command);
            record.put("description", description);
            record.put("timestamp", System.currentTimeMillis());
            record.put("time", new Date());

            // 获取现有历史记录
            List<Object> history = RedisUtils.getCacheList(cacheKey);
            if (history == null) {
                history = new ArrayList<>();
            }

            // 添加新记录到列表开头
            history.add(0, record);

            // 保持最近100条记录
            if (history.size() > 100) {
                history = history.subList(0, 100);
            }

            // 重新设置缓存
            RedisUtils.setCacheList(cacheKey, history);

        } catch (Exception e) {
            log.error("Failed to record command history for device {}: {}", imei, e.getMessage(), e);
        }
    }

    // 以下是构建各种指令的方法，根据GT06协议规范实现
    private String buildRestartCommand() {
        return "RESET#";
    }

    private String buildResetCommand() {
        return "FACTORY#";
    }

    private String buildIntervalCommand(Integer interval) {
        return String.format("TIMER,%d#", interval);
    }

    private String buildApnCommand(String apn, String username, String password) {
        if (StrUtil.isBlank(username) && StrUtil.isBlank(password)) {
            return String.format("APN,%s#", apn);
        } else {
            return String.format("APN,%s,%s,%s#", apn, 
                    StrUtil.isBlank(username) ? "" : username,
                    StrUtil.isBlank(password) ? "" : password);
        }
    }

    private String buildServerCommand(String serverIp, Integer port) {
        return String.format("SERVER,1,%s,%d#", serverIp, port);
    }

    private String buildStatusQueryCommand() {
        return "STATUS#";
    }

    private String buildTimezoneCommand(Integer timezone) {
        return String.format("TIMEZONE,E,%d#", timezone);
    }

    private String buildWorkModeCommand(Integer mode) {
        return String.format("MODE,%d#", mode);
    }
}
