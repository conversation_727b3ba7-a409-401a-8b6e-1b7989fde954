package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.constant.IotConstants;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06协议解码器测试
 * 验证修复后的解码器能够正确处理各种数据包
 *
 * <AUTHOR>
 */
@DisplayName("GT06协议解码器测试")
public class GT06ProtocolDecoderTest {

    private GT06ProtocolDecoder decoder;

    @BeforeEach
    void setUp() {
        decoder = new GT06ProtocolDecoder();
    }

    @Test
    @DisplayName("测试登录包解码 - concox-master成功案例")
    void testDecodeLoginPacket() {
        // 使用concox-master成功解析的登录包数据
        String hexData = "78781101035873905215859020203201001f49170d0a";
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
        
        List<Object> out = new ArrayList<>();
        
        try {
            decoder.decode(null, buffer, out);
            
            // 验证解码成功
            assertEquals(1, out.size(), "应该解码出1个消息");
            
            IotMessage message = (IotMessage) out.get(0);
            assertEquals((byte) 0x01, message.getProtocol(), "协议号应该是0x01(登录包)");
            assertNotNull(message.getImei(), "IMEI不应该为空");
            assertTrue(message.getImei().length() >= 15, "IMEI长度应该至少15位");
            
            System.out.println("✅ 登录包解码成功:");
            System.out.println("  协议号: 0x" + String.format("%02X", message.getProtocol() & 0xFF));
            System.out.println("  IMEI: " + message.getImei());
            System.out.println("  序列号: " + message.getSequenceNumber());
            
        } catch (Exception e) {
            fail("解码过程不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试心跳包解码")
    void testDecodeHeartbeatPacket() {
        // 心跳包数据 (协议号0x13)
        String hexData = "78780B23C0019F040001000818720D0A";
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
        
        List<Object> out = new ArrayList<>();
        
        try {
            decoder.decode(null, buffer, out);
            
            assertEquals(1, out.size(), "应该解码出1个消息");
            
            IotMessage message = (IotMessage) out.get(0);
            assertEquals((byte) 0x23, message.getProtocol(), "协议号应该是0x23(心跳包)");
            
            System.out.println("✅ 心跳包解码成功:");
            System.out.println("  协议号: 0x" + String.format("%02X", message.getProtocol() & 0xFF));
            System.out.println("  序列号: " + message.getSequenceNumber());
            
        } catch (Exception e) {
            fail("解码过程不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试定位数据包解码")
    void testDecodeLocationPacket() {
        // 定位数据包 (协议号0x12)
        String hexData = "787822120c0e08041e9e016323990853fb390435260381000d0a";
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
        
        List<Object> out = new ArrayList<>();
        
        try {
            decoder.decode(null, buffer, out);
            
            assertEquals(1, out.size(), "应该解码出1个消息");
            
            IotMessage message = (IotMessage) out.get(0);
            assertEquals((byte) 0x12, message.getProtocol(), "协议号应该是0x12(定位包)");
            
            System.out.println("✅ 定位包解码成功:");
            System.out.println("  协议号: 0x" + String.format("%02X", message.getProtocol() & 0xFF));
            System.out.println("  序列号: " + message.getSequenceNumber());
            
        } catch (Exception e) {
            fail("解码过程不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试数据不足情况")
    void testInsufficientData() {
        // 不完整的数据包
        String hexData = "787811";
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
        
        List<Object> out = new ArrayList<>();
        
        try {
            decoder.decode(null, buffer, out);
            
            // 数据不足时应该不产生消息，等待更多数据
            assertEquals(0, out.size(), "数据不足时不应该产生消息");
            
            System.out.println("✅ 数据不足处理正确: 等待更多数据");
            
        } catch (Exception e) {
            fail("数据不足时不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试无效起始位处理")
    void testInvalidStartFlag() {
        // 无效起始位的数据
        String hexData = "12345678901234567890";
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
        
        List<Object> out = new ArrayList<>();
        
        try {
            decoder.decode(null, buffer, out);
            
            // 无效起始位应该被跳过，不产生消息
            assertEquals(0, out.size(), "无效起始位不应该产生消息");
            
            System.out.println("✅ 无效起始位处理正确: 跳过无效数据");
            
        } catch (Exception e) {
            fail("无效起始位处理不应该抛出异常: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试channelRead0触发 - 集成测试")
    void testChannelRead0Triggered() {
        // 创建嵌入式通道进行集成测试
        EmbeddedChannel channel = new EmbeddedChannel(
            new GT06ProtocolDecoder()
        );

        // 发送登录包
        String loginHex = "78781101035873905215859020203201001f49170d0a";
        ByteBuf loginData = Unpooled.wrappedBuffer(hexStringToBytes(loginHex));

        // 写入数据并验证处理
        boolean result = channel.writeInbound(loginData);
        assertTrue(result, "数据应该被成功处理");

        // 读取解码后的消息
        IotMessage message = channel.readInbound();
        assertNotNull(message, "应该有解码后的消息");
        assertEquals((byte) 0x01, message.getProtocol(), "协议号应该是0x01");

        System.out.println("✅ channelRead0触发测试成功:");
        System.out.println("  消息已成功传递到下一个处理器");
        System.out.println("  协议号: 0x" + String.format("%02X", message.getProtocol() & 0xFF));

        channel.close();
    }

    @Test
    @DisplayName("测试修复后的解码器性能")
    void testDecoderPerformance() {
        // 测试大量数据包的解码性能
        String[] testPackets = {
            "78781101035873905215859020203201001f49170d0a", // 登录包
            "78780B23C0019F040001000818720D0A",             // 心跳包
            "787822120c0e08041e9e016323990853fb390435260381000d0a" // 定位包
        };

        long startTime = System.currentTimeMillis();
        int totalPackets = 0;
        int successCount = 0;

        for (int i = 0; i < 100; i++) { // 测试100轮
            for (String hexData : testPackets) {
                ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(hexData));
                List<Object> out = new ArrayList<>();

                try {
                    decoder.decode(null, buffer, out);
                    totalPackets++;
                    if (!out.isEmpty()) {
                        successCount++;
                    }
                } catch (Exception e) {
                    // 记录但不中断测试
                    System.err.println("解码异常: " + e.getMessage());
                }
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("✅ 性能测试结果:");
        System.out.println("  总数据包: " + totalPackets);
        System.out.println("  成功解码: " + successCount);
        System.out.println("  成功率: " + String.format("%.2f%%", (double) successCount / totalPackets * 100));
        System.out.println("  耗时: " + duration + "ms");
        System.out.println("  平均每包: " + String.format("%.2f", (double) duration / totalPackets) + "ms");

        // 验证性能指标
        assertTrue(successCount > totalPackets * 0.95, "成功率应该大于95%");
        assertTrue(duration < totalPackets * 2, "平均每包处理时间应该小于2ms");
    }

    @Test
    @DisplayName("测试多个数据包连续解码")
    void testMultiplePacketsDecode() {
        // 连续的多个数据包
        String packet1 = "78781101035873905215859020203201001f49170d0a"; // 登录包
        String packet2 = "78780B23C0019F040001000818720D0A";             // 心跳包
        
        String combinedHex = packet1 + packet2;
        ByteBuf buffer = Unpooled.wrappedBuffer(hexStringToBytes(combinedHex));
        
        List<Object> out = new ArrayList<>();
        
        try {
            // 第一次解码
            decoder.decode(null, buffer, out);
            assertEquals(1, out.size(), "应该解码出第一个消息");
            
            IotMessage message1 = (IotMessage) out.get(0);
            assertEquals((byte) 0x01, message1.getProtocol(), "第一个消息应该是登录包");
            
            // 第二次解码
            out.clear();
            decoder.decode(null, buffer, out);
            assertEquals(1, out.size(), "应该解码出第二个消息");
            
            IotMessage message2 = (IotMessage) out.get(0);
            assertEquals((byte) 0x23, message2.getProtocol(), "第二个消息应该是心跳包");
            
            System.out.println("✅ 多数据包连续解码成功:");
            System.out.println("  第一个包: 0x" + String.format("%02X", message1.getProtocol() & 0xFF));
            System.out.println("  第二个包: 0x" + String.format("%02X", message2.getProtocol() & 0xFF));
            
        } catch (Exception e) {
            fail("多数据包解码不应该抛出异常: " + e.getMessage());
        }
    }

    /**
     * 将hex字符串转换为字节数组
     */
    private byte[] hexStringToBytes(String hexString) {
        hexString = hexString.replaceAll("[^0-9A-Fa-f]", "");
        
        if (hexString.length() % 2 != 0) {
            throw new IllegalArgumentException("Hex字符串长度必须是偶数");
        }
        
        byte[] bytes = new byte[hexString.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            bytes[i] = (byte) Integer.parseInt(hexString.substring(index, index + 2), 16);
        }
        
        return bytes;
    }
}
