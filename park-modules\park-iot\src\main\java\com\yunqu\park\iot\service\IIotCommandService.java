package com.yunqu.park.iot.service;

import com.yunqu.park.iot.controller.IotCommandController.BatchCommandRequest;

/**
 * IoT设备指令服务接口
 *
 * <AUTHOR>
 */
public interface IIotCommandService {

    /**
     * 发送设备重启指令
     * @param imei 设备IMEI号
     * @return 是否成功
     */
    Boolean sendRestartCommand(String imei);

    /**
     * 发送设备复位指令
     * @param imei 设备IMEI号
     * @return 是否成功
     */
    Boolean sendResetCommand(String imei);

    /**
     * 设置设备上报间隔
     * @param imei 设备IMEI号
     * @param interval 间隔时间(秒)
     * @return 是否成功
     */
    Boolean setReportInterval(String imei, Integer interval);

    /**
     * 设置设备APN参数
     * @param imei 设备IMEI号
     * @param apn APN名称
     * @param username 用户名
     * @param password 密码
     * @return 是否成功
     */
    Boolean setApnConfig(String imei, String apn, String username, String password);

    /**
     * 设置设备服务器地址
     * @param imei 设备IMEI号
     * @param serverIp 服务器IP
     * @param port 端口
     * @return 是否成功
     */
    Boolean setServerAddress(String imei, String serverIp, Integer port);

    /**
     * 查询设备状态
     * @param imei 设备IMEI号
     * @return 是否成功
     */
    Boolean queryDeviceStatus(String imei);

    /**
     * 设置设备时区
     * @param imei 设备IMEI号
     * @param timezone 时区偏移(小时)
     * @return 是否成功
     */
    Boolean setTimezone(String imei, Integer timezone);

    /**
     * 设置设备工作模式
     * @param imei 设备IMEI号
     * @param mode 工作模式
     * @return 是否成功
     */
    Boolean setWorkMode(String imei, Integer mode);

    /**
     * 发送自定义指令
     * @param imei 设备IMEI号
     * @param command 指令内容
     * @return 是否成功
     */
    Boolean sendCustomCommand(String imei, String command);

    /**
     * 获取设备指令发送历史
     * @param imei 设备IMEI号
     * @param limit 限制数量
     * @return 指令历史
     */
    Object getCommandHistory(String imei, Integer limit);

    /**
     * 批量发送指令
     * @param request 批量指令请求
     * @return 发送结果
     */
    Object sendBatchCommand(BatchCommandRequest request);
}
