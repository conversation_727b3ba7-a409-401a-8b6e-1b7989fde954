package com.yunqu.park.iot.netty.server;

import com.yunqu.park.iot.constant.IotLogMarkers;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * IoT TCP服务器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "iot.tcp-server", name = "enabled", havingValue = "true")
public class IotTcpServer {

    @Value("${iot.tcp-server.port:8888}")
    private int port;

    @Value("${iot.tcp-server.boss-threads:1}")
    private int bossThreads;

    @Value("${iot.tcp-server.worker-threads:8}")
    private int workerThreads;

    @Value("${iot.tcp-server.so-backlog:128}")
    private int soBacklog;

    @Value("${iot.tcp-server.so-keepalive:true}")
    private boolean soKeepAlive;

    @Value("${iot.tcp-server.tcp-nodelay:true}")
    private boolean tcpNoDelay;

    @Value("${iot.device.heartbeat-timeout:300}")
    private int heartbeatTimeout;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private ChannelFuture channelFuture;

    @PostConstruct
    public void start() {
        try {
            log.info("[TCP-SERVER] 🚀 Starting IoT TCP Server...");
            log.info("[TCP-SERVER] Server configuration: Port={}, BossThreads={}, WorkerThreads={}, HeartbeatTimeout={}s",
                    port, bossThreads, workerThreads, heartbeatTimeout);
            log.info("[TCP-SERVER] Network options: SO_BACKLOG={}, SO_KEEPALIVE={}, TCP_NODELAY={}",
                    soBacklog, soKeepAlive, tcpNoDelay);

            // 创建线程组
            log.debug("[TCP-SERVER] Creating thread groups...");
            bossGroup = new NioEventLoopGroup(bossThreads, new DefaultThreadFactory("iot-boss"));
            workerGroup = new NioEventLoopGroup(workerThreads, new DefaultThreadFactory("iot-worker"));
            log.debug("[TCP-SERVER] Thread groups created successfully");

            // 创建服务器启动器
            log.debug("[TCP-SERVER] Configuring server bootstrap...");
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new IotChannelInitializer(heartbeatTimeout))
                    .option(ChannelOption.SO_BACKLOG, soBacklog)
                    .childOption(ChannelOption.SO_KEEPALIVE, soKeepAlive)
                    .childOption(ChannelOption.TCP_NODELAY, tcpNoDelay)
                    .childOption(ChannelOption.SO_REUSEADDR, true);

            // 绑定端口并启动服务器
            log.info("[TCP-SERVER] Binding to port {}...", port);
            channelFuture = bootstrap.bind(port).sync();

            log.info(IotLogMarkers.IOT_SERVER, "[TCP-SERVER] ✅ IoT TCP Server started successfully!");
            log.info(IotLogMarkers.IOT_SERVER, "[TCP-SERVER] 📡 Server listening on port: {}", port);
            log.info(IotLogMarkers.IOT_SERVER, "[TCP-SERVER] 🔧 Ready to accept GT06 device connections");

        } catch (Exception e) {
            log.error("[TCP-SERVER] ❌ Failed to start IoT TCP Server: {}", e.getMessage(), e);
            shutdown();
            throw new RuntimeException("Failed to start IoT TCP Server", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        try {
            log.info("[TCP-SERVER] 🛑 Shutting down IoT TCP Server...");

            if (channelFuture != null) {
                log.debug("[TCP-SERVER] Closing server channel...");
                channelFuture.channel().close().sync();
                log.debug("[TCP-SERVER] Server channel closed");
            }

            if (workerGroup != null) {
                log.debug("[TCP-SERVER] Shutting down worker group...");
                workerGroup.shutdownGracefully().sync();
                log.debug("[TCP-SERVER] Worker group shutdown completed");
            }

            if (bossGroup != null) {
                log.debug("[TCP-SERVER] Shutting down boss group...");
                bossGroup.shutdownGracefully().sync();
                log.debug("[TCP-SERVER] Boss group shutdown completed");
            }

            log.info("[TCP-SERVER] ✅ IoT TCP Server shutdown completed successfully");

        } catch (Exception e) {
            log.error("[TCP-SERVER] ❌ Error during server shutdown: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查服务器是否正在运行
     */
    public boolean isRunning() {
        return channelFuture != null && channelFuture.channel().isActive();
    }

    /**
     * 获取服务器端口
     */
    public int getPort() {
        return port;
    }

    /**
     * 获取连接数统计
     */
    public int getConnectionCount() {
        // TODO: 实现连接数统计
        return 0;
    }
}
