package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.pool.IotMessagePool;
import com.yunqu.park.iot.utils.CrcUtils;
import com.yunqu.park.iot.utils.ByteBufUtils;
import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.common.core.utils.SpringUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.List;

/**
 * GT06协议解码器
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolDecoder extends ByteToMessageDecoder {

    @Override
    public void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 记录原始数据用于调试
        int originalReadableBytes = in.readableBytes();

        if (originalReadableBytes < IotConstants.GT06Protocol.MIN_PACKET_LENGTH) {
            log.debug("[PROTOCOL-DECODE] Insufficient data: available={}, required={}",
                     originalReadableBytes, IotConstants.GT06Protocol.MIN_PACKET_LENGTH);
            return; // 数据不足，等待更多数据
        }

        in.markReaderIndex();

        try {
            log.debug("[PROTOCOL-DECODE] 🔍 开始解码数据包: RemoteAddress={}, AvailableBytes={}",
                     ctx.channel().remoteAddress(), originalReadableBytes);

            // 1. 检查起始位 (支持0x7878和0x7979)
            byte[] startFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (startFlag == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取起始位，等待更多数据");
                return;
            }

            if (!Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7878) &&
                !Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7979)) {
                in.resetReaderIndex();
                in.skipBytes(1); // 跳过一个字节继续寻找
                log.debug("[PROTOCOL-DECODE] 无效起始位: {}, 跳过1字节继续寻找",
                         ByteBufUtils.bytesToHexString(startFlag));
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 起始位验证通过: {}", ByteBufUtils.bytesToHexString(startFlag));

            // 2. 读取包长度
            Byte packetLengthByte = ByteBufUtils.safeReadByte(in);
            if (packetLengthByte == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据不足，无法读取包长度字节");
                return;
            }

            byte packetLength = packetLengthByte;
            int packetLengthValue = packetLength & 0xFF;
            log.debug("[PROTOCOL-DECODE] 📏 包长度字段值: {}, 剩余可读字节: {}",
                     packetLengthValue, in.readableBytes());

            // 3. 修复：正确验证数据完整性
            // GT06协议：包长度字段 = 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            // 总数据包 = 起始位(2) + 包长度(1) + 数据内容(packetLengthValue) + 停止位(2)
            int remainingDataLength = packetLengthValue; // 包长度字段的值就是剩余数据长度
            if (in.readableBytes() < remainingDataLength + 2) { // +2 for stop flag
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] 数据包不完整: 需要{}字节(数据{}+停止位2), 可用{}字节, 等待更多数据",
                         remainingDataLength + 2, remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据完整性验证通过: 需要{}字节, 可用{}字节",
                     remainingDataLength + 2, in.readableBytes());

            // 4. 修复：一次性读取所有数据（不包含停止位）
            byte[] packetData = ByteBufUtils.safeReadBytes(in, remainingDataLength);
            if (packetData == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取数据包内容: 需要{}字节, 可用{}字节",
                        remainingDataLength, in.readableBytes());
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 数据包内容读取成功: {}字节, 数据={}",
                     packetData.length, ByteBufUtils.bytesToHexString(packetData));

            // 5. 修复：验证停止位（不抛异常，改为跳过处理）
            byte[] stopFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (stopFlag == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] ❌ 无法读取停止位: 可用{}字节", in.readableBytes());
                return;
            }

            if (!Arrays.equals(stopFlag, IotConstants.GT06Protocol.STOP_FLAG)) {
                log.warn("[PROTOCOL-DECODE] ⚠️ 停止位不匹配: 期望={}, 实际={}, 跳过此数据包",
                        ByteBufUtils.bytesToHexString(IotConstants.GT06Protocol.STOP_FLAG),
                        ByteBufUtils.bytesToHexString(stopFlag));
                // 不抛异常，跳过这个字节继续处理
                in.resetReaderIndex();
                in.skipBytes(1);
                return;
            }

            log.debug("[PROTOCOL-DECODE] ✅ 停止位验证通过: {}", ByteBufUtils.bytesToHexString(stopFlag));

            // 6. 修复：CRC校验（宽松模式，失败不中断处理）
            boolean crcValid = performCrcValidation(packetData, ctx);
            log.debug("[PROTOCOL-DECODE] CRC校验结果: {}", crcValid ? "通过" : "失败(继续处理)");

            // 7. 解析协议内容并构造消息对象
            IotMessage message = parseMessage(startFlag, packetData, ctx);
            if (message != null) {
                out.add(message);
                log.info("[PROTOCOL-DECODE] ✅ 数据包解码成功: Protocol=0x{}, IMEI={}, SequenceNumber={}, RemoteAddress={}",
                         String.format("%02X", message.getProtocol() & 0xFF),
                         message.getImei(), message.getSequenceNumber(), ctx.channel().remoteAddress());
            } else {
                log.warn("[PROTOCOL-DECODE] ❌ 消息解析失败: RemoteAddress={}, 数据={}",
                        ctx.channel().remoteAddress(), ByteBufUtils.bytesToHexString(packetData));
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] ❌ 解码过程异常: RemoteAddress={}, Error={}, OriginalBytes={}, CurrentBytes={}",
                     ctx.channel().remoteAddress(), e.getMessage(), originalReadableBytes, in.readableBytes(), e);

            // 输出原始数据用于调试
            in.resetReaderIndex();
            if (in.readableBytes() > 0) {
                String hexData = ByteBufUtils.bufToHexString(in, Math.min(64, in.readableBytes()));
                log.debug("[PROTOCOL-DECODE] 原始数据(前64字节): {}", hexData);
            }

            // 跳过一个字节继续尝试解析，不抛异常中断连接
            if (in.readableBytes() > 0) {
                in.skipBytes(1);
                log.debug("[PROTOCOL-DECODE] 跳过1字节，继续解码处理");
            }
        }
    }

    /**
     * 解析消息内容
     * 修复：正确解析GT06协议数据包结构
     */
    private IotMessage parseMessage(byte[] startFlag, byte[] packetData, ChannelHandlerContext ctx) {
        if (packetData.length < 4) {
            log.warn("[PROTOCOL-DECODE] 数据包太短，无法解析: 长度={}", packetData.length);
            return null;
        }

        try {
            // 阶段2优化：使用对象池获取消息对象
            IotMessagePool messagePool = SpringUtils.getBean(IotMessagePool.class);
            IotMessage message = messagePool != null ? messagePool.borrowMessage() : new IotMessage();
            message.setStartFlag(startFlag);

            // GT06协议数据包结构：包长度(1) + 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            message.setPacketLength(packetData[0] & 0xFF);

            // 解析协议号
            byte protocol = packetData[1];
            message.setProtocol(protocol);

            log.debug("[PROTOCOL-DECODE] 解析协议号: 0x{} ({})",
                     String.format("%02X", protocol & 0xFF), message.getProtocolName());

            // 修复：正确计算内容长度
            // 数据包结构：包长度(1) + 协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            int contentLength = packetData.length - 5; // 减去包长度(1) + 协议号(1) + 序列号(2) + CRC(2)
            if (contentLength > 0) {
                byte[] content = new byte[contentLength];
                System.arraycopy(packetData, 2, content, 0, contentLength);
                message.setContent(content);

                log.debug("[PROTOCOL-DECODE] 内容数据: 长度={}, 数据={}",
                         contentLength, ByteBufUtils.bytesToHexString(content));

                // 解析IMEI(如果是登录包)
                if (protocol == IotConstants.GT06Protocol.PROTOCOL_LOGIN && contentLength >= 8) {
                    String imei = parseImeiFromLoginPacket(content);
                    message.setImei(imei);
                    log.debug("[PROTOCOL-DECODE] 解析IMEI: {}", imei);
                }
            }

            // 解析序列号（大端序）
            int sequenceNumber = ((packetData[packetData.length - 4] & 0xFF) << 8) |
                               (packetData[packetData.length - 3] & 0xFF);
            message.setSequenceNumber(sequenceNumber);

            // 解析CRC（大端序）
            int crc = ((packetData[packetData.length - 2] & 0xFF) << 8) |
                     (packetData[packetData.length - 1] & 0xFF);
            message.setCrc(crc);

            message.setStopFlag(IotConstants.GT06Protocol.STOP_FLAG);

            // 设置客户端信息
            String remoteAddress = ctx.channel().remoteAddress().toString();
            if (remoteAddress.startsWith("/")) {
                remoteAddress = remoteAddress.substring(1);
            }
            String[] parts = remoteAddress.split(":");
            message.setClientIp(parts[0]);
            if (parts.length > 1) {
                try {
                    message.setClientPort(Integer.parseInt(parts[1]));
                } catch (NumberFormatException e) {
                    message.setClientPort(0);
                }
            }

            log.debug("[PROTOCOL-DECODE] 消息解析完成: Protocol=0x{}, IMEI={}, SequenceNumber={}, CRC=0x{}",
                     String.format("%02X", protocol & 0xFF), message.getImei(),
                     sequenceNumber, String.format("%04X", crc));

            return message;

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] 消息解析异常: {}, 数据={}",
                     e.getMessage(), ByteBufUtils.bytesToHexString(packetData), e);
            return null;
        }
    }

    /**
     * 从登录包中解析IMEI
     */
    private String parseImeiFromLoginPacket(byte[] content) {
        if (content.length < 8) {
            return null;
        }

        // IMEI通常在登录包的前8个字节，以BCD编码
        StringBuilder imei = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            int high = (content[i] >> 4) & 0x0F;
            int low = content[i] & 0x0F;
            imei.append(high).append(low);
        }

        // 移除最后一位校验位，IMEI为15位
        String result = imei.toString();
        return result.length() > 15 ? result.substring(0, 15) : result;
    }

    /**
     * 执行CRC校验
     * 修复：采用宽松模式，校验失败不中断处理流程
     * @param packetData 数据包内容
     * @param ctx 通道上下文
     * @return 校验是否通过
     */
    private boolean performCrcValidation(byte[] packetData, ChannelHandlerContext ctx) {
        try {
            // 获取配置
            IotProtocolConfig config = getProtocolConfig();

            if (!config.getCrc().isEnabled()) {
                log.debug("[PROTOCOL-DECODE] CRC校验已禁用");
                return true;
            }

            boolean crcValid;
            if (config.getCrc().isLenientMode()) {
                crcValid = CrcUtils.validateCrcLenient(packetData);
                log.debug("[PROTOCOL-DECODE] 使用宽松模式CRC校验");
            } else {
                crcValid = CrcUtils.validateCrc(packetData);
                log.debug("[PROTOCOL-DECODE] 使用严格模式CRC校验");
            }

            if (!crcValid) {
                if (config.getCrc().isVerboseLogging()) {
                    log.warn("[PROTOCOL-DECODE] ⚠️ CRC校验失败: RemoteAddress={}, PacketLength={}, Data={}",
                            ctx.channel().remoteAddress(), packetData.length,
                            ByteBufUtils.bytesToHexString(packetData));
                }

                // 修复：即使CRC校验失败也继续处理，不抛异常
                log.warn("[PROTOCOL-DECODE] ⚠️ CRC校验失败，但继续处理数据包 (宽松模式)");
                return false;
            } else {
                if (config.getCrc().isVerboseLogging()) {
                    log.debug("[PROTOCOL-DECODE] ✅ CRC校验通过");
                }
                return true;
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] CRC校验异常: {}, 继续处理数据包", e.getMessage(), e);
            // 异常情况下也继续处理，不中断数据流
            return false;
        }
    }

    /**
     * 获取协议配置
     * @return 协议配置对象
     */
    private IotProtocolConfig getProtocolConfig() {
        try {
            return SpringUtils.getBean(IotProtocolConfig.class);
        } catch (Exception e) {
            log.debug("Failed to get protocol config, using defaults: {}", e.getMessage());
            // 返回默认配置
            IotProtocolConfig defaultConfig = new IotProtocolConfig();
            defaultConfig.getCrc().setEnabled(true);
            defaultConfig.getCrc().setLenientMode(true);
            defaultConfig.getCrc().setContinueOnFailure(true);
            defaultConfig.getCrc().setVerboseLogging(true);
            return defaultConfig;
        }
    }

    /**
     * 经纬度解析 - 按协议规范实现
     * 公式: (度数×60+分数)×30000 = 十进制值
     */
    public static double parseCoordinate(byte[] coordinateBytes) {
        if (coordinateBytes.length != 4) {
            return 0.0;
        }
        int value = ByteBuffer.wrap(coordinateBytes).getInt();
        return value / 30000.0 / 60.0; // 转换回度数
    }


}
