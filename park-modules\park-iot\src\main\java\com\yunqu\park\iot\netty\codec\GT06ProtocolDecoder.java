package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.utils.CrcUtils;
import com.yunqu.park.iot.utils.ByteBufUtils;
import com.yunqu.park.iot.config.IotProtocolConfig;
import com.yunqu.park.common.core.utils.SpringUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.List;

/**
 * GT06协议解码器
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolDecoder extends ByteToMessageDecoder {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 记录原始数据用于调试
        int originalReadableBytes = in.readableBytes();

        if (originalReadableBytes < IotConstants.GT06Protocol.MIN_PACKET_LENGTH) {
            log.debug("[PROTOCOL-DECODE] Insufficient data: available={}, required={}",
                     originalReadableBytes, IotConstants.GT06Protocol.MIN_PACKET_LENGTH);
            return; // 数据不足，等待更多数据
        }

        in.markReaderIndex();

        try {
            // 1. 检查起始位 (支持0x7878和0x7979)
            byte[] startFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (startFlag == null) {
                in.resetReaderIndex();
                return;
            }

            if (!Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7878) &&
                !Arrays.equals(startFlag, IotConstants.GT06Protocol.START_FLAG_7979)) {
                in.resetReaderIndex();
                in.skipBytes(1); // 跳过一个字节继续寻找
                log.debug("[PROTOCOL-DECODE] Invalid start flag: {}, skipping 1 byte", Arrays.toString(startFlag));
                return;
            }

            // 2. 读取包长度
            Byte packetLengthByte = ByteBufUtils.safeReadByte(in);
            if (packetLengthByte == null) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] No packet length byte available");
                return;
            }

            byte packetLength = packetLengthByte;
            int packetLengthValue = packetLength & 0xFF;

            // GT06协议：包长度 = 协议号(1) + 内容长度 + 序列号(2) + CRC(2)
            // 总长度 = 起始位(2) + 包长度(1) + 包内容 + 停止位(2)
            int totalPacketLength = 2 + 1 + packetLengthValue + 2; // 完整包长度

            log.debug("[PROTOCOL-DECODE] Packet info: startFlag={}, packetLength={}, totalLength={}, available={}",
                     Arrays.toString(startFlag), packetLengthValue, totalPacketLength, originalReadableBytes);

            // 3. 验证数据完整性 - 检查是否有足够的数据
            int remainingBytesNeeded = totalPacketLength - 3; // 已读取起始位(2) + 包长度(1)
            if (in.readableBytes() < remainingBytesNeeded) {
                in.resetReaderIndex();
                log.debug("[PROTOCOL-DECODE] Incomplete packet: need={}, available={}",
                         remainingBytesNeeded, in.readableBytes());
                return;
            }

            // 4. 读取包内容（不包含停止位）
            byte[] packetData = ByteBufUtils.safeReadBytes(in, packetLengthValue);
            if (packetData == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] Cannot read packet data: need={}, available={}",
                        packetLengthValue, in.readableBytes());
                return;
            }

            // 5. 验证停止位
            byte[] stopFlag = ByteBufUtils.safeReadBytes(in, 2);
            if (stopFlag == null) {
                in.resetReaderIndex();
                log.warn("[PROTOCOL-DECODE] Cannot read stop flag: available={}", in.readableBytes());
                return;
            }
            if (!Arrays.equals(stopFlag, IotConstants.GT06Protocol.STOP_FLAG)) {
                log.warn("[PROTOCOL-DECODE] Invalid stop flag: expected={}, actual={}",
                        Arrays.toString(IotConstants.GT06Protocol.STOP_FLAG), Arrays.toString(stopFlag));
                throw new IllegalArgumentException("Invalid stop flag");
            }

            // 6. CRC校验
            performCrcValidation(packetData, ctx);

            // 7. 解析协议内容并构造消息对象
            IotMessage message = parseMessage(startFlag, packetData, ctx);
            if (message != null) {
                out.add(message);
                log.debug("[PROTOCOL-DECODE] ✅ Message decoded successfully: Protocol=0x{}, IMEI={}, SequenceNumber={}",
                         String.format("%02X", message.getProtocol() & 0xFF),
                         message.getImei(), message.getSequenceNumber());
            } else {
                log.warn("[PROTOCOL-DECODE] ❌ Failed to parse message: RemoteAddress={}", ctx.channel().remoteAddress());
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] ❌ Failed to decode message: RemoteAddress={}, Error={}, OriginalBytes={}, CurrentBytes={}",
                     ctx.channel().remoteAddress(), e.getMessage(), originalReadableBytes, in.readableBytes());

            // 输出原始数据用于调试
            in.resetReaderIndex();
            if (in.readableBytes() > 0) {
                String hexData = ByteBufUtils.bufToHexString(in, 32);
                log.debug("[PROTOCOL-DECODE] Raw data (first 32 bytes): {}", hexData);
            }

            in.skipBytes(1); // 跳过错误字节
            log.debug("[PROTOCOL-DECODE] Skipped 1 byte, continuing decode process");
        }
    }

    /**
     * 解析消息内容
     */
    private IotMessage parseMessage(byte[] startFlag, byte[] packetData, ChannelHandlerContext ctx) {
        if (packetData.length < 4) {
            return null;
        }

        IotMessage message = new IotMessage();
        message.setStartFlag(startFlag);
        message.setPacketLength(packetData[0] & 0xFF);

        // 解析协议号
        byte protocol = packetData[1];
        message.setProtocol(protocol);

        // 解析内容和序列号
        int contentLength = packetData.length - 5; // 减去包长度(1) + 协议号(1) + 序列号(2) + CRC(2)
        if (contentLength > 0) {
            byte[] content = new byte[contentLength];
            System.arraycopy(packetData, 2, content, 0, contentLength);
            message.setContent(content);

            // 解析IMEI(如果是登录包)
            if (protocol == IotConstants.GT06Protocol.PROTOCOL_LOGIN && contentLength >= 8) {
                message.setImei(parseImeiFromLoginPacket(content));
            }
        }

        // 解析序列号
        int sequenceNumber = ((packetData[packetData.length - 4] & 0xFF) << 8) |
                           (packetData[packetData.length - 3] & 0xFF);
        message.setSequenceNumber(sequenceNumber);

        // 解析CRC
        int crc = ((packetData[packetData.length - 2] & 0xFF) << 8) |
                 (packetData[packetData.length - 1] & 0xFF);
        message.setCrc(crc);

        message.setStopFlag(IotConstants.GT06Protocol.STOP_FLAG);

        // 设置客户端信息
        String remoteAddress = ctx.channel().remoteAddress().toString();
        if (remoteAddress.startsWith("/")) {
            remoteAddress = remoteAddress.substring(1);
        }
        String[] parts = remoteAddress.split(":");
        message.setClientIp(parts[0]);
        if (parts.length > 1) {
            try {
                message.setClientPort(Integer.parseInt(parts[1]));
            } catch (NumberFormatException e) {
                message.setClientPort(0);
            }
        }

        return message;
    }

    /**
     * 从登录包中解析IMEI
     */
    private String parseImeiFromLoginPacket(byte[] content) {
        if (content.length < 8) {
            return null;
        }

        // IMEI通常在登录包的前8个字节，以BCD编码
        StringBuilder imei = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            int high = (content[i] >> 4) & 0x0F;
            int low = content[i] & 0x0F;
            imei.append(high).append(low);
        }

        // 移除最后一位校验位，IMEI为15位
        String result = imei.toString();
        return result.length() > 15 ? result.substring(0, 15) : result;
    }

    /**
     * 执行CRC校验
     * @param packetData 数据包内容
     * @param ctx 通道上下文
     * @return 校验是否通过
     */
    private boolean performCrcValidation(byte[] packetData, ChannelHandlerContext ctx) {
        try {
            // 获取配置
            IotProtocolConfig config = getProtocolConfig();

            if (!config.getCrc().isEnabled()) {
                log.debug("[PROTOCOL-DECODE] CRC validation disabled by configuration");
                return true;
            }

            boolean crcValid;
            if (config.getCrc().isLenientMode()) {
                crcValid = CrcUtils.validateCrcLenient(packetData);
            } else {
                crcValid = CrcUtils.validateCrc(packetData);
            }

            if (!crcValid) {
                if (config.getCrc().isVerboseLogging()) {
                    log.warn("[PROTOCOL-DECODE] ❌ CRC validation failed: RemoteAddress={}, PacketLength={}, Data={}",
                            ctx.channel().remoteAddress(), packetData.length,
                            ByteBufUtils.bytesToHexString(packetData));
                }

                if (!config.getCrc().isContinueOnFailure()) {
                    throw new IllegalArgumentException("CRC validation failed");
                }

                log.warn("[PROTOCOL-DECODE] ⚠️ Continuing despite CRC validation failure (configured)");
                return false;
            } else {
                if (config.getCrc().isVerboseLogging()) {
                    log.debug("[PROTOCOL-DECODE] ✅ CRC validation passed");
                }
                return true;
            }

        } catch (Exception e) {
            log.error("[PROTOCOL-DECODE] CRC validation error: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取协议配置
     * @return 协议配置对象
     */
    private IotProtocolConfig getProtocolConfig() {
        try {
            return SpringUtils.getBean(IotProtocolConfig.class);
        } catch (Exception e) {
            log.debug("Failed to get protocol config, using defaults: {}", e.getMessage());
            // 返回默认配置
            IotProtocolConfig defaultConfig = new IotProtocolConfig();
            defaultConfig.getCrc().setEnabled(true);
            defaultConfig.getCrc().setLenientMode(true);
            defaultConfig.getCrc().setContinueOnFailure(true);
            defaultConfig.getCrc().setVerboseLogging(true);
            return defaultConfig;
        }
    }

    /**
     * 经纬度解析 - 按协议规范实现
     * 公式: (度数×60+分数)×30000 = 十进制值
     */
    public static double parseCoordinate(byte[] coordinateBytes) {
        if (coordinateBytes.length != 4) {
            return 0.0;
        }
        int value = ByteBuffer.wrap(coordinateBytes).getInt();
        return value / 30000.0 / 60.0; // 转换回度数
    }


}
