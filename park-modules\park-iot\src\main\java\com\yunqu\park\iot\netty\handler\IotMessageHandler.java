package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.common.core.utils.SpringUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.codec.GT06ProtocolEncoder;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.service.IIotProtocolService;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * IoT消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class IotMessageHandler extends SimpleChannelInboundHandler<IotMessage> {

    private IIotProtocolService protocolService;
    private DeviceConnectionManager connectionManager;

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        super.handlerAdded(ctx);
        // 延迟获取Spring Bean，避免循环依赖
        if (protocolService == null) {
            protocolService = SpringUtils.getBean(IIotProtocolService.class);
        }
        if (connectionManager == null) {
            connectionManager = SpringUtils.getBean(DeviceConnectionManager.class);
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, IotMessage msg) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        try {
            log.info("[MESSAGE-RECEIVED] 📨 Received message: RemoteAddress={}, ChannelId={}, Protocol=0x{}, IMEI={}, SequenceNumber={}",
                    remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF),
                    msg.getImei(), msg.getSequenceNumber());

            log.debug("[MESSAGE-RECEIVED] Message details: {}", msg);

            // 处理协议消息
            handleProtocolMessage(ctx, msg);

            // 发送响应(如果需要)
            if (msg.needResponse()) {
                log.debug("[MESSAGE-RESPONSE] Sending response for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
                sendResponse(ctx, msg);
            } else {
                log.debug("[MESSAGE-RESPONSE] No response needed for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
            }

        } catch (Exception e) {
            log.error("[MESSAGE-ERROR] ❌ Error processing message: RemoteAddress={}, ChannelId={}, Protocol=0x{}, Error={}",
                     remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
        }
    }

    /**
     * 处理协议消息
     */
    private void handleProtocolMessage(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            switch (msg.getProtocol()) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    protocolService.handleLoginMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LOCATION:
                    protocolService.handleLocationMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT:
                    protocolService.handleHeartbeatMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ALARM:
                    protocolService.handleAlarmMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ADDRESS_QUERY:
                    protocolService.handleAddressQueryMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LBS:
                    protocolService.handleLbsMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LBS_WIFI:
                    protocolService.handleLbsWifiMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_IMSI:
                    protocolService.handleImsiMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ICCID:
                    protocolService.handleIccidMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    protocolService.handleRecordMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_TERMINAL_RESPONSE:
                    protocolService.handleTerminalResponseMessage(msg, ctx);
                    break;
                default:
                    log.warn("Unsupported protocol: 0x{} from {}", 
                            String.format("%02X", msg.getProtocol() & 0xFF), 
                            ctx.channel().remoteAddress());
            }
        } catch (Exception e) {
            log.error("Error handling protocol message: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送响应消息
     */
    private void sendResponse(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            byte[] responseData = null;

            switch (msg.getProtocol()) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    responseData = GT06ProtocolEncoder.buildLoginResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT:
                    responseData = GT06ProtocolEncoder.buildHeartbeatResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ALARM:
                    responseData = GT06ProtocolEncoder.buildAlarmResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    // 录音协议包响应，使用相同的序列号
                    responseData = GT06ProtocolEncoder.buildAlarmResponse(msg.getSequenceNumber());
                    break;
                default:
                    log.debug("No response needed for protocol: 0x{}", 
                            String.format("%02X", msg.getProtocol() & 0xFF));
                    return;
            }

            if (responseData != null) {
                ctx.writeAndFlush(Unpooled.wrappedBuffer(responseData));
                log.debug("Sent response for protocol 0x{} to {}", 
                        String.format("%02X", msg.getProtocol() & 0xFF), 
                        ctx.channel().remoteAddress());
            }

        } catch (Exception e) {
            log.error("Error sending response: {}", e.getMessage(), e);
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        log.info("[CHANNEL-ACTIVE] 🔗 New device connection established: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);
        log.debug("[CHANNEL-ACTIVE] Channel details: LocalAddress={}, IsActive={}, IsOpen={}",
                 ctx.channel().localAddress(), ctx.channel().isActive(), ctx.channel().isOpen());

        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        log.info("[CHANNEL-INACTIVE] 🔌 Device connection closed: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);

        // 从连接管理器中移除设备
        if (connectionManager != null) {
            String imei = connectionManager.getDeviceImei(ctx.channel());
            if (imei != null) {
                log.info("[CHANNEL-INACTIVE] Removing device from connection manager: IMEI={}", imei);
            }
            connectionManager.removeDeviceByChannel(ctx.channel());
        }

        // 处理设备离线
        if (protocolService != null) {
            log.debug("[CHANNEL-INACTIVE] Processing device disconnection through protocol service");
            protocolService.handleDeviceDisconnected(ctx);
        }

        log.info("[CHANNEL-INACTIVE] ✅ Device disconnection processed: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);

        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        log.error("[CHANNEL-EXCEPTION] ⚠️ Exception caught in channel: RemoteAddress={}, ChannelId={}, Exception={}",
                 remoteAddress, channelId, cause.getClass().getSimpleName());
        log.error("[CHANNEL-EXCEPTION] Exception details: {}", cause.getMessage(), cause);

        // 获取设备IMEI用于日志追踪
        if (connectionManager != null) {
            String imei = connectionManager.getDeviceImei(ctx.channel());
            if (imei != null) {
                log.error("[CHANNEL-EXCEPTION] Device IMEI affected: {}", imei);
            }
        }

        log.info("[CHANNEL-EXCEPTION] Closing channel due to exception: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);
        ctx.close();
    }
}
