package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.common.core.utils.SpringUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.codec.GT06ProtocolEncoder;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.service.IIotProtocolService;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * IoT消息处理器
 *
 * <AUTHOR>
 */
@Slf4j
public class IotMessageHandler extends SimpleChannelInboundHandler<IotMessage> {

    private IIotProtocolService protocolService;
    private DeviceConnectionManager connectionManager;

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
        super.handlerAdded(ctx);
        // 延迟获取Spring Bean，避免循环依赖
        if (protocolService == null) {
            protocolService = SpringUtils.getBean(IIotProtocolService.class);
        }
        if (connectionManager == null) {
            connectionManager = SpringUtils.getBean(DeviceConnectionManager.class);
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, IotMessage msg) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        try {
            log.info("[MESSAGE-RECEIVED] 📨 接收到消息: RemoteAddress={}, ChannelId={}, Protocol=0x{}, IMEI={}, SequenceNumber={}",
                    remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF),
                    msg.getImei(), msg.getSequenceNumber());

            log.debug("[MESSAGE-RECEIVED] 消息详情: {}", msg);

            // 阶段3增强：添加消息处理前的验证
            if (!validateMessage(msg)) {
                log.warn("[MESSAGE-RECEIVED] ⚠️ 消息验证失败，跳过处理: RemoteAddress={}, Protocol=0x{}",
                        remoteAddress, String.format("%02X", msg.getProtocol() & 0xFF));
                return;
            }

            // 处理协议消息
            handleProtocolMessage(ctx, msg);

            // 发送响应(如果需要)
            if (msg.needResponse()) {
                log.info("[MESSAGE-RESPONSE] Sending response for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
                sendResponse(ctx, msg);
            } else {
                log.info("[MESSAGE-RESPONSE] No response needed for protocol: 0x{}",
                         String.format("%02X", msg.getProtocol() & 0xFF));
            }

        } catch (Exception e) {
            log.error("[MESSAGE-ERROR] ❌ Error processing message: RemoteAddress={}, ChannelId={}, Protocol=0x{}, Error={}",
                     remoteAddress, channelId, String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
        }
    }

    /**
     * 处理协议消息
     */
    private void handleProtocolMessage(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            switch (msg.getProtocol()) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    protocolService.handleLoginMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LOCATION:
                    protocolService.handleLocationMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT:
                    protocolService.handleHeartbeatMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ALARM:
                    protocolService.handleAlarmMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ADDRESS_QUERY:
                    protocolService.handleAddressQueryMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LBS:
                    protocolService.handleLbsMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_LBS_WIFI:
                    protocolService.handleLbsWifiMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_IMSI:
                    protocolService.handleImsiMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ICCID:
                    protocolService.handleIccidMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    protocolService.handleRecordMessage(msg, ctx);
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_TERMINAL_RESPONSE:
                    protocolService.handleTerminalResponseMessage(msg, ctx);
                    break;
                default:
                    log.warn("Unsupported protocol: 0x{} from {}", 
                            String.format("%02X", msg.getProtocol() & 0xFF), 
                            ctx.channel().remoteAddress());
            }
        } catch (Exception e) {
            log.error("Error handling protocol message: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送响应消息
     */
    private void sendResponse(ChannelHandlerContext ctx, IotMessage msg) {
        try {
            byte[] responseData = null;

            switch (msg.getProtocol()) {
                case IotConstants.GT06Protocol.PROTOCOL_LOGIN:
                    responseData = GT06ProtocolEncoder.buildLoginResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT:
                    responseData = GT06ProtocolEncoder.buildHeartbeatResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_ALARM:
                    responseData = GT06ProtocolEncoder.buildAlarmResponse(msg.getSequenceNumber());
                    break;
                case IotConstants.GT06Protocol.PROTOCOL_RECORD:
                    // 录音协议包响应，使用相同的序列号
                    responseData = GT06ProtocolEncoder.buildAlarmResponse(msg.getSequenceNumber());
                    break;
                default:
                    log.debug("No response needed for protocol: 0x{}", 
                            String.format("%02X", msg.getProtocol() & 0xFF));
                    return;
            }

            if (responseData != null) {
                ctx.writeAndFlush(Unpooled.wrappedBuffer(responseData));
                log.debug("Sent response for protocol 0x{} to {}", 
                        String.format("%02X", msg.getProtocol() & 0xFF), 
                        ctx.channel().remoteAddress());
            }

        } catch (Exception e) {
            log.error("Error sending response: {}", e.getMessage(), e);
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        log.info("[CHANNEL-ACTIVE] 🔗 New device connection established: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);
        log.debug("[CHANNEL-ACTIVE] Channel details: LocalAddress={}, IsActive={}, IsOpen={}",
                 ctx.channel().localAddress(), ctx.channel().isActive(), ctx.channel().isOpen());

        super.channelActive(ctx);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        log.info("[CHANNEL-INACTIVE] 🔌 Device connection closed: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);

        // 从连接管理器中移除设备
        if (connectionManager != null) {
            String imei = connectionManager.getDeviceImei(ctx.channel());
            if (imei != null) {
                log.info("[CHANNEL-INACTIVE] Removing device from connection manager: IMEI={}", imei);
            }
            connectionManager.removeDeviceByChannel(ctx.channel());
        }

        // 处理设备离线
        if (protocolService != null) {
            log.debug("[CHANNEL-INACTIVE] Processing device disconnection through protocol service");
            protocolService.handleDeviceDisconnected(ctx);
        }

        log.info("[CHANNEL-INACTIVE] ✅ Device disconnection processed: RemoteAddress={}, ChannelId={}",
                remoteAddress, channelId);

        super.channelInactive(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        String channelId = ctx.channel().id().asShortText();

        // 阶段3增强：分类处理不同类型的异常
        if (cause instanceof java.io.IOException) {
            log.warn("[CHANNEL-EXCEPTION] 🔌 网络IO异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());
            handleNetworkException(ctx, cause);
        } else if (cause instanceof IllegalArgumentException) {
            log.warn("[CHANNEL-EXCEPTION] ⚠️ 协议解析异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());
            handleProtocolException(ctx, cause);
        } else if (cause instanceof OutOfMemoryError) {
            log.error("[CHANNEL-EXCEPTION] 💥 内存不足异常: RemoteAddress={}, ChannelId={}, Error={}",
                    remoteAddress, channelId, cause.getMessage());
            handleMemoryException(ctx, cause);
        } else {
            log.error("[CHANNEL-EXCEPTION] ❌ 未知异常: RemoteAddress={}, ChannelId={}, Exception={}",
                    remoteAddress, channelId, cause.getClass().getSimpleName());
            log.error("[CHANNEL-EXCEPTION] 异常详情: {}", cause.getMessage(), cause);
            handleUnknownException(ctx, cause);
        }

        // 获取设备IMEI用于日志追踪
        if (connectionManager != null) {
            String imei = connectionManager.getDeviceImei(ctx.channel());
            if (imei != null) {
                log.error("[CHANNEL-EXCEPTION] 受影响的设备IMEI: {}", imei);
            }
        }

        // 阶段3增强：确保资源清理
        cleanupChannelResources(ctx);
    }

    /**
     * 阶段3增强：消息验证方法
     * @param message 待验证的消息
     * @return 验证是否通过
     */
    private boolean validateMessage(IotMessage message) {
        if (message == null) {
            log.warn("[MESSAGE-VALIDATE] 消息对象为空");
            return false;
        }

        // 验证协议号
        if (message.getProtocol() == 0) {
            log.warn("[MESSAGE-VALIDATE] 协议号无效: {}", message.getProtocol());
            return false;
        }

        // 验证序列号
        if (message.getSequenceNumber() < 0) {
            log.warn("[MESSAGE-VALIDATE] 序列号无效: {}", message.getSequenceNumber());
            return false;
        }

        // 对于登录包，验证IMEI
        if (message.getProtocol() == IotConstants.GT06Protocol.PROTOCOL_LOGIN) {
            if (message.getImei() == null || message.getImei().trim().isEmpty()) {
                log.warn("[MESSAGE-VALIDATE] 登录包IMEI为空");
                return false;
            }
            if (message.getImei().length() < 15) {
                log.warn("[MESSAGE-VALIDATE] IMEI长度不足: {}", message.getImei());
                return false;
            }
        }

        return true;
    }

    /**
     * 阶段3增强：处理网络异常
     */
    private void handleNetworkException(ChannelHandlerContext ctx, Throwable cause) {
        log.debug("[EXCEPTION-HANDLER] 处理网络异常，正常关闭连接");
        // 网络异常通常是客户端断开连接，正常处理即可
    }

    /**
     * 阶段3增强：处理协议异常
     */
    private void handleProtocolException(ChannelHandlerContext ctx, Throwable cause) {
        log.debug("[EXCEPTION-HANDLER] 处理协议异常，继续保持连接");
        // 协议异常不关闭连接，给客户端重新发送的机会
    }

    /**
     * 阶段3增强：处理内存异常
     */
    private void handleMemoryException(ChannelHandlerContext ctx, Throwable cause) {
        log.error("[EXCEPTION-HANDLER] 内存不足，立即关闭连接并清理资源");
        // 内存异常需要立即关闭连接
        ctx.close();
    }

    /**
     * 阶段3增强：处理未知异常
     */
    private void handleUnknownException(ChannelHandlerContext ctx, Throwable cause) {
        log.error("[EXCEPTION-HANDLER] 未知异常，关闭连接");
        ctx.close();
    }

    /**
     * 阶段3增强：清理通道资源
     */
    private void cleanupChannelResources(ChannelHandlerContext ctx) {
        try {
            // 从连接管理器移除设备
            if (connectionManager != null) {
                connectionManager.removeDeviceByChannel(ctx.channel());
            }

            // 清理通道属性
            ctx.channel().attr(io.netty.util.AttributeKey.valueOf("device_imei")).set(null);

            log.debug("[EXCEPTION-HANDLER] 通道资源清理完成");

        } catch (Exception e) {
            log.error("[EXCEPTION-HANDLER] 清理通道资源时发生异常: {}", e.getMessage(), e);
        }
    }
}
