package com.yunqu.park.iot.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.domain.IotDevice;
import com.yunqu.park.iot.domain.IotLocationData;
import com.yunqu.park.iot.domain.IotAlarmRecord;
import com.yunqu.park.iot.netty.codec.GT06ProtocolDecoder;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.service.IIotDeviceService;
import com.yunqu.park.iot.service.IIotProtocolService;
import com.yunqu.park.iot.service.IIotLocationDataService;
import com.yunqu.park.iot.service.IIotAlarmRecordService;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.netty.handler.CommandResponseHandler;
import io.netty.channel.ChannelHandlerContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IoT协议处理服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IotProtocolServiceImpl implements IIotProtocolService {

    private final IIotDeviceService deviceService;
    private final IIotLocationDataService locationDataService;
    private final IIotAlarmRecordService alarmRecordService;
    private final DeviceConnectionManager connectionManager;
    private final CommandResponseHandler commandResponseHandler;

    // 存储通道与IMEI的映射关系
    private final ConcurrentHashMap<String, String> channelImeiMap = new ConcurrentHashMap<>();

    @Override
    public void handleLoginMessage(IotMessage message, ChannelHandlerContext ctx) {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        try {
            String imei = message.getImei();
            if (StrUtil.isBlank(imei)) {
                log.warn("[PROTOCOL-LOGIN] ❌ Login message without IMEI: RemoteAddress={}, ChannelId={}",
                        remoteAddress, channelId);
                return;
            }

            log.info("[PROTOCOL-LOGIN] 🔐 Processing device login: IMEI={}, ClientIP={}, RemoteAddress={}, ChannelId={}",
                    imei, message.getClientIp(), remoteAddress, channelId);

            // 检查设备是否存在
            boolean deviceExists = deviceService.existsByImei(imei);
            log.debug("[PROTOCOL-LOGIN] Device exists check: IMEI={}, Exists={}", imei, deviceExists);

            if (!deviceExists) {
                log.info("[PROTOCOL-LOGIN] Auto-registering new device: IMEI={}", imei);
                registerNewDevice(imei, message);
            }

            // 处理设备上线
            log.debug("[PROTOCOL-LOGIN] Updating device online status: IMEI={}", imei);
            deviceService.handleDeviceOnline(imei, message.getClientIp());

            // 在连接管理器中注册设备连接
            log.debug("[PROTOCOL-LOGIN] Registering device connection: IMEI={}, ChannelId={}", imei, channelId);
            connectionManager.registerDevice(imei, ctx.channel());

            // 记录通道与IMEI的映射
            channelImeiMap.put(channelId, imei);
            log.debug("[PROTOCOL-LOGIN] Channel-IMEI mapping added: ChannelId={}, IMEI={}", channelId, imei);

            log.info("[PROTOCOL-LOGIN] ✅ Device login successful: IMEI={}, ClientIP={}, ChannelId={}",
                    imei, message.getClientIp(), channelId);

        } catch (Exception e) {
            log.error("[PROTOCOL-LOGIN] ❌ Error handling login message: RemoteAddress={}, ChannelId={}, Error={}",
                     remoteAddress, channelId, e.getMessage(), e);
        }
    }

    @Override
    public void handleLocationMessage(IotMessage message, ChannelHandlerContext ctx) {
        String remoteAddress = ctx.channel().remoteAddress().toString();
        String channelId = ctx.channel().id().asShortText();

        try {
            String imei = getImeiFromChannel(ctx);
            if (StrUtil.isBlank(imei)) {
                log.warn("[PROTOCOL-LOCATION] ❌ Location message without IMEI: RemoteAddress={}, ChannelId={}",
                        remoteAddress, channelId);
                return;
            }

            log.info("[PROTOCOL-LOCATION] 📍 Processing location data: IMEI={}, SequenceNumber={}, ContentLength={}",
                    imei, message.getSequenceNumber(),
                    message.getContent() != null ? message.getContent().length : 0);

            // 解析定位数据并存储
            parseAndSaveLocationData(message, imei);

            // 推送实时位置数据
            // pushLocationData(imei, locationData);

            log.debug("[PROTOCOL-LOCATION] ✅ Location data processed: IMEI={}", imei);

        } catch (Exception e) {
            log.error("[PROTOCOL-LOCATION] ❌ Error handling location message: IMEI={}, RemoteAddress={}, Error={}",
                     getImeiFromChannel(ctx), remoteAddress, e.getMessage(), e);
        }
    }

    @Override
    public void handleHeartbeatMessage(IotMessage message, ChannelHandlerContext ctx) {
        String remoteAddress = ctx.channel().remoteAddress().toString();

        try {
            String imei = getImeiFromChannel(ctx);
            if (StrUtil.isBlank(imei)) {
                log.warn("[PROTOCOL-HEARTBEAT] ❌ Heartbeat message without IMEI: RemoteAddress={}", remoteAddress);
                return;
            }

            log.debug("[PROTOCOL-HEARTBEAT] 💓 Received heartbeat: IMEI={}, SequenceNumber={}",
                     imei, message.getSequenceNumber());

            // 更新设备最后在线时间
            deviceService.handleDeviceOnline(imei, message.getClientIp());

            log.debug("[PROTOCOL-HEARTBEAT] ✅ Heartbeat processed: IMEI={}", imei);

        } catch (Exception e) {
            log.error("[PROTOCOL-HEARTBEAT] ❌ Error handling heartbeat message: IMEI={}, RemoteAddress={}, Error={}",
                     getImeiFromChannel(ctx), remoteAddress, e.getMessage(), e);
        }
    }

    @Override
    public void handleAlarmMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            if (StrUtil.isBlank(imei)) {
                log.warn("Alarm message without IMEI from {}", ctx.channel().remoteAddress());
                return;
            }

            log.info("Received alarm from device: {}", imei);

            // 解析报警数据并存储
            parseAndSaveAlarmData(message, imei);

            // 推送报警信息
            // pushAlarmData(imei, alarmData);

        } catch (Exception e) {
            log.error("Error handling alarm message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleAddressQueryMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            log.debug("Received address query from device: {}", imei);
            // 地址查询消息处理（无需响应）
        } catch (Exception e) {
            log.error("Error handling address query message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleLbsMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            log.debug("Received LBS data from device: {}", imei);
            // LBS多基站信息处理（无需响应）
        } catch (Exception e) {
            log.error("Error handling LBS message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleLbsWifiMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            log.debug("Received LBS+WIFI data from device: {}", imei);
            // LBS+WIFI信息处理（无需响应）
        } catch (Exception e) {
            log.error("Error handling LBS+WIFI message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleImsiMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            if (StrUtil.isNotBlank(imei) && message.getContent() != null) {
                // TODO: 解析IMSI号并更新设备信息
                String imsi = parseImsiFromContent(message.getContent());
                if (StrUtil.isNotBlank(imsi)) {
                    deviceService.updateDeviceSimInfo(imei, imsi, null);
                    log.info("Updated IMSI for device {}: {}", imei, imsi);
                }
            }
        } catch (Exception e) {
            log.error("Error handling IMSI message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleIccidMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            if (StrUtil.isNotBlank(imei) && message.getContent() != null) {
                // TODO: 解析ICCID号并更新设备信息
                String iccid = parseIccidFromContent(message.getContent());
                if (StrUtil.isNotBlank(iccid)) {
                    deviceService.updateDeviceSimInfo(imei, null, iccid);
                    log.info("Updated ICCID for device {}: {}", imei, iccid);
                }
            }
        } catch (Exception e) {
            log.error("Error handling ICCID message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleRecordMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            log.info("Received record message from device: {}", imei);
            // 录音协议包处理（需要响应）
        } catch (Exception e) {
            log.error("Error handling record message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleTerminalResponseMessage(IotMessage message, ChannelHandlerContext ctx) {
        try {
            String imei = getImeiFromChannel(ctx);
            log.debug("Received terminal response from device: {}", imei);

            // 处理终端指令响应
            if (StrUtil.isNotBlank(imei)) {
                commandResponseHandler.handleCommandResponse(message, imei);
            }
        } catch (Exception e) {
            log.error("Error handling terminal response message: {}", e.getMessage(), e);
        }
    }

    @Override
    public void handleDeviceDisconnected(ChannelHandlerContext ctx) {
        try {
            String channelId = ctx.channel().id().asShortText();
            String imei = channelImeiMap.remove(channelId);

            if (StrUtil.isNotBlank(imei)) {
                deviceService.handleDeviceOffline(imei);
                log.info("Device {} disconnected", imei);
            }
        } catch (Exception e) {
            log.error("Error handling device disconnection: {}", e.getMessage(), e);
        }
    }

    /**
     * 从通道获取IMEI
     */
    private String getImeiFromChannel(ChannelHandlerContext ctx) {
        String channelId = ctx.channel().id().asShortText();
        return channelImeiMap.get(channelId);
    }

    /**
     * 自动注册新设备
     */
    private void registerNewDevice(String imei, IotMessage message) {
        try {
            IotDevice device = new IotDevice();
            device.setImei(imei);
            device.setDeviceName("GT06设备-" + imei);
            device.setDeviceType("GT06");
            device.setStatus(IotConstants.DeviceStatus.OFFLINE);
            device.setRegisterTime(new Date());
            device.setCreateTime(new Date());

            // 保存设备信息
            boolean saved = deviceService.save(device);
            if (saved) {
                log.info("Auto registered new device: {}", imei);
            } else {
                log.error("Failed to save new device: {}", imei);
            }
        } catch (Exception e) {
            log.error("Failed to register new device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 从内容中解析IMSI号
     */
    private String parseImsiFromContent(byte[] content) {
        // TODO: 实现IMSI解析逻辑
        return null;
    }

    /**
     * 从内容中解析ICCID号
     */
    private String parseIccidFromContent(byte[] content) {
        // TODO: 实现ICCID解析逻辑
        return null;
    }

    /**
     * 解析并保存定位数据
     */
    private void parseAndSaveLocationData(IotMessage message, String imei) {
        try {
            byte[] content = message.getContent();
            if (content == null || content.length < 20) {
                log.warn("Invalid location data content length from device: {}", imei);
                return;
            }

            // 获取设备信息
            IotDevice device = deviceService.getDeviceByImei(imei);
            if (device == null) {
                log.warn("Device not found for IMEI: {}", imei);
                return;
            }

            // 解析定位数据
            IotLocationData locationData = new IotLocationData();
            locationData.setDeviceId(device.getDeviceId());
            locationData.setImei(imei);
            locationData.setTenantId(device.getTenantId());

            // 解析GPS时间 (6字节: 年月日时分秒)
            if (content.length >= 6) {
                int year = 2000 + (content[0] & 0xFF);
                int month = content[1] & 0xFF;
                int day = content[2] & 0xFF;
                int hour = content[3] & 0xFF;
                int minute = content[4] & 0xFF;
                int second = content[5] & 0xFF;

                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.set(year, month - 1, day, hour, minute, second);
                locationData.setGpsTime(cal.getTime());
            }

            // 解析GPS信息字节
            if (content.length >= 7) {
                byte gpsInfo = content[6];
                locationData.setSatelliteCount((gpsInfo >> 4) & 0x0F);
                locationData.setGpsStatus(((gpsInfo & 0x02) != 0) ? "1" : "0");
            }

            // 解析纬度 (4字节)
            if (content.length >= 11) {
                byte[] latBytes = new byte[4];
                System.arraycopy(content, 7, latBytes, 0, 4);
                double latitude = GT06ProtocolDecoder.parseCoordinate(latBytes);
                locationData.setLatitude(java.math.BigDecimal.valueOf(latitude));
            }

            // 解析经度 (4字节)
            if (content.length >= 15) {
                byte[] lngBytes = new byte[4];
                System.arraycopy(content, 11, lngBytes, 0, 4);
                double longitude = GT06ProtocolDecoder.parseCoordinate(lngBytes);
                locationData.setLongitude(java.math.BigDecimal.valueOf(longitude));
            }

            // 解析速度
            if (content.length >= 16) {
                locationData.setSpeed(content[15] & 0xFF);
            }

            // 解析方向和状态
            if (content.length >= 18) {
                int courseStatus = ((content[16] & 0xFF) << 8) | (content[17] & 0xFF);
                locationData.setDirection(courseStatus & 0x03FF);
                locationData.setAccStatus(((courseStatus & 0x2000) != 0) ? "1" : "0");
            }

            // 保存定位数据
            locationDataService.saveLocationData(locationData);
            log.debug("Saved location data for device: {}", imei);

        } catch (Exception e) {
            log.error("Failed to parse and save location data for device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析并保存报警数据
     */
    private void parseAndSaveAlarmData(IotMessage message, String imei) {
        try {
            byte[] content = message.getContent();
            if (content == null || content.length < 20) {
                log.warn("Invalid alarm data content length from device: {}", imei);
                return;
            }

            // 获取设备信息
            IotDevice device = deviceService.getDeviceByImei(imei);
            if (device == null) {
                log.warn("Device not found for IMEI: {}", imei);
                return;
            }

            // 解析报警数据
            IotAlarmRecord alarmRecord = new IotAlarmRecord();
            alarmRecord.setDeviceId(device.getDeviceId());
            alarmRecord.setImei(imei);
            alarmRecord.setTenantId(device.getTenantId());

            // 解析报警时间 (6字节: 年月日时分秒)
            if (content.length >= 6) {
                int year = 2000 + (content[0] & 0xFF);
                int month = content[1] & 0xFF;
                int day = content[2] & 0xFF;
                int hour = content[3] & 0xFF;
                int minute = content[4] & 0xFF;
                int second = content[5] & 0xFF;

                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.set(year, month - 1, day, hour, minute, second);
                alarmRecord.setAlarmTime(cal.getTime());
            }

            // 解析报警类型 (通常在GPS信息字节中)
            if (content.length >= 7) {
                byte gpsInfo = content[6];
                // 报警类型可能需要根据具体协议调整解析位置
                int alarmType = gpsInfo & 0xFF;
                alarmRecord.setAlarmType(alarmType);
            }

            // 解析报警位置 - 纬度 (4字节)
            if (content.length >= 11) {
                byte[] latBytes = new byte[4];
                System.arraycopy(content, 7, latBytes, 0, 4);
                double latitude = GT06ProtocolDecoder.parseCoordinate(latBytes);
                alarmRecord.setLatitude(java.math.BigDecimal.valueOf(latitude));
            }

            // 解析报警位置 - 经度 (4字节)
            if (content.length >= 15) {
                byte[] lngBytes = new byte[4];
                System.arraycopy(content, 11, lngBytes, 0, 4);
                double longitude = GT06ProtocolDecoder.parseCoordinate(lngBytes);
                alarmRecord.setLongitude(java.math.BigDecimal.valueOf(longitude));
            }

            // 保存报警记录
            alarmRecordService.saveAlarmRecord(alarmRecord);
            log.info("Saved alarm record for device: {}, type: {}", imei, alarmRecord.getAlarmType());

        } catch (Exception e) {
            log.error("Failed to parse and save alarm data for device {}: {}", imei, e.getMessage(), e);
        }
    }
}
