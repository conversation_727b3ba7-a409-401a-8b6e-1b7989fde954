package com.yunqu.park.iot.service;

import com.yunqu.park.iot.netty.protocol.IotMessage;
import io.netty.channel.ChannelHandlerContext;

/**
 * IoT协议处理服务接口
 *
 * <AUTHOR>
 */
public interface IIotProtocolService {

    /**
     * 处理设备登录消息
     * @param message 登录消息
     * @param ctx 通道上下文
     */
    void handleLoginMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理定位数据消息
     * @param message 定位数据消息
     * @param ctx 通道上下文
     */
    void handleLocationMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理心跳消息
     * @param message 心跳消息
     * @param ctx 通道上下文
     */
    void handleHeartbeatMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理报警消息
     * @param message 报警消息
     * @param ctx 通道上下文
     */
    void handleAlarmMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理地址查询消息
     * @param message 地址查询消息
     * @param ctx 通道上下文
     */
    void handleAddressQueryMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理LBS多基站信息消息
     * @param message LBS消息
     * @param ctx 通道上下文
     */
    void handleLbsMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理LBS+WIFI信息消息
     * @param message LBS+WIFI消息
     * @param ctx 通道上下文
     */
    void handleLbsWifiMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理IMSI号上报消息
     * @param message IMSI消息
     * @param ctx 通道上下文
     */
    void handleImsiMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理ICCID号上报消息
     * @param message ICCID消息
     * @param ctx 通道上下文
     */
    void handleIccidMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理录音协议消息
     * @param message 录音消息
     * @param ctx 通道上下文
     */
    void handleRecordMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理终端指令响应消息
     * @param message 终端响应消息
     * @param ctx 通道上下文
     */
    void handleTerminalResponseMessage(IotMessage message, ChannelHandlerContext ctx);

    /**
     * 处理设备断开连接
     * @param ctx 通道上下文
     */
    void handleDeviceDisconnected(ChannelHandlerContext ctx);
}
