package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.utils.ByteBufUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.embedded.EmbeddedChannel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GT06协议编码器测试
 * 验证优化后的编码器功能和性能
 *
 * <AUTHOR>
 */
@DisplayName("GT06协议编码器测试")
public class GT06ProtocolEncoderTest {

    private GT06ProtocolEncoder encoder;

    @BeforeEach
    void setUp() {
        encoder = new GT06ProtocolEncoder();
    }

    @Test
    @DisplayName("测试登录响应包编码")
    void testBuildLoginResponse() {
        int sequenceNumber = 12345;
        
        byte[] response = GT06ProtocolEncoder.buildLoginResponse(sequenceNumber);
        
        assertNotNull(response, "响应数据不应该为空");
        assertTrue(response.length > 0, "响应数据长度应该大于0");
        
        // 验证起始位
        assertEquals((byte) 0x78, response[0], "起始位第一字节应该是0x78");
        assertEquals((byte) 0x78, response[1], "起始位第二字节应该是0x78");
        
        // 验证协议号
        assertEquals(IotConstants.GT06Protocol.PROTOCOL_LOGIN, response[3], "协议号应该是登录协议");
        
        // 验证停止位
        assertEquals((byte) 0x0D, response[response.length - 2], "停止位第一字节应该是0x0D");
        assertEquals((byte) 0x0A, response[response.length - 1], "停止位第二字节应该是0x0A");
        
        System.out.println("✅ 登录响应包编码成功:");
        System.out.println("  数据长度: " + response.length + " 字节");
        System.out.println("  序列号: " + sequenceNumber);
        System.out.println("  数据: " + bytesToHexString(response));
    }

    @Test
    @DisplayName("测试心跳响应包编码")
    void testBuildHeartbeatResponse() {
        int sequenceNumber = 54321;
        
        byte[] response = GT06ProtocolEncoder.buildHeartbeatResponse(sequenceNumber);
        
        assertNotNull(response, "响应数据不应该为空");
        assertTrue(response.length > 0, "响应数据长度应该大于0");
        
        // 验证协议号
        assertEquals(IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT, response[3], "协议号应该是心跳协议");
        
        System.out.println("✅ 心跳响应包编码成功:");
        System.out.println("  数据长度: " + response.length + " 字节");
        System.out.println("  序列号: " + sequenceNumber);
        System.out.println("  数据: " + bytesToHexString(response));
    }

    @Test
    @DisplayName("测试报警响应包编码")
    void testBuildAlarmResponse() {
        int sequenceNumber = 98765;
        
        byte[] response = GT06ProtocolEncoder.buildAlarmResponse(sequenceNumber);
        
        assertNotNull(response, "响应数据不应该为空");
        assertTrue(response.length > 0, "响应数据长度应该大于0");
        
        // 验证协议号
        assertEquals(IotConstants.GT06Protocol.PROTOCOL_ALARM, response[3], "协议号应该是报警协议");
        
        System.out.println("✅ 报警响应包编码成功:");
        System.out.println("  数据长度: " + response.length + " 字节");
        System.out.println("  序列号: " + sequenceNumber);
        System.out.println("  数据: " + bytesToHexString(response));
    }

    @Test
    @DisplayName("测试带时间的登录响应包编码")
    void testBuildLoginResponseWithTime() {
        int sequenceNumber = 11111;
        
        byte[] response = GT06ProtocolEncoder.buildLoginResponseWithTime(sequenceNumber);
        
        assertNotNull(response, "响应数据不应该为空");
        assertTrue(response.length > 0, "响应数据长度应该大于0");
        
        // 带时间的响应包应该比普通响应包长
        byte[] normalResponse = GT06ProtocolEncoder.buildLoginResponse(sequenceNumber);
        assertTrue(response.length > normalResponse.length, "带时间的响应包应该更长");
        
        // 验证协议号
        assertEquals(IotConstants.GT06Protocol.PROTOCOL_LOGIN, response[3], "协议号应该是登录协议");
        
        System.out.println("✅ 带时间的登录响应包编码成功:");
        System.out.println("  数据长度: " + response.length + " 字节");
        System.out.println("  普通响应长度: " + normalResponse.length + " 字节");
        System.out.println("  序列号: " + sequenceNumber);
        System.out.println("  数据: " + bytesToHexString(response));
    }

    @Test
    @DisplayName("测试编码器集成测试")
    void testEncoderIntegration() {
        // 创建嵌入式通道进行集成测试
        EmbeddedChannel channel = new EmbeddedChannel(encoder);
        
        // 创建测试消息
        IotMessage message = new IotMessage();
        message.setProtocol(IotConstants.GT06Protocol.PROTOCOL_LOGIN);
        message.setSequenceNumber(12345);
        message.setContent(null);
        
        // 编码消息
        boolean result = channel.writeOutbound(message);
        assertTrue(result, "消息应该被成功编码");
        
        // 读取编码后的数据
        ByteBuf encoded = channel.readOutbound();
        assertNotNull(encoded, "应该有编码后的数据");
        assertTrue(encoded.readableBytes() > 0, "编码后的数据长度应该大于0");
        
        // 验证编码后的数据格式
        byte[] data = new byte[encoded.readableBytes()];
        encoded.readBytes(data);
        
        // 验证起始位
        assertEquals((byte) 0x78, data[0], "起始位第一字节应该是0x78");
        assertEquals((byte) 0x78, data[1], "起始位第二字节应该是0x78");
        
        System.out.println("✅ 编码器集成测试成功:");
        System.out.println("  编码后数据长度: " + data.length + " 字节");
        System.out.println("  数据: " + bytesToHexString(data));
        
        encoded.release();
        channel.close();
    }

    @Test
    @DisplayName("测试编码器性能")
    void testEncoderPerformance() {
        int testCount = 1000;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            // 测试不同类型的响应包编码
            GT06ProtocolEncoder.buildLoginResponse(i);
            GT06ProtocolEncoder.buildHeartbeatResponse(i);
            GT06ProtocolEncoder.buildAlarmResponse(i);
            
            if (i % 100 == 0) {
                GT06ProtocolEncoder.buildLoginResponseWithTime(i);
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        System.out.println("✅ 编码器性能测试结果:");
        System.out.println("  测试次数: " + (testCount * 3 + testCount / 100) + " 次编码");
        System.out.println("  总耗时: " + duration + " ms");
        System.out.println("  平均每次: " + String.format("%.3f", (double) duration / (testCount * 3)) + " ms");
        
        // 验证性能指标
        assertTrue(duration < testCount, "平均每次编码时间应该小于1ms");
    }

    @Test
    @DisplayName("测试编码器统计信息")
    void testEncoderStatistics() {
        String statistics = GT06ProtocolEncoder.getEncoderStatistics();

        assertNotNull(statistics, "统计信息不应该为空");
        assertFalse(statistics.trim().isEmpty(), "统计信息不应该为空字符串");

        System.out.println("✅ 编码器统计信息:");
        System.out.println("  " + statistics);
    }

    @Test
    @DisplayName("测试concox-master规范一致性")
    void testConcoxMasterCompliance() {
        // 测试登录响应包格式是否符合concox-master规范
        int sequenceNumber = 12345; // 0x3039

        byte[] response = GT06ProtocolEncoder.buildLoginResponse(sequenceNumber);

        // 验证数据包结构
        assertNotNull(response, "响应数据不应该为空");
        assertTrue(response.length >= 10, "响应数据长度应该至少10字节");

        // 验证起始位 (7878)
        assertEquals((byte) 0x78, response[0], "起始位第一字节应该是0x78");
        assertEquals((byte) 0x78, response[1], "起始位第二字节应该是0x78");

        // 验证包长度 (应该是0x05，表示协议号1+序列号2+CRC2=5字节)
        assertEquals((byte) 0x05, response[2], "包长度应该是0x05");

        // 验证协议号 (登录响应是0x01)
        assertEquals((byte) 0x01, response[3], "协议号应该是0x01");

        // 验证序列号 (大端序)
        int actualSequenceNumber = ((response[4] & 0xFF) << 8) | (response[5] & 0xFF);
        assertEquals(sequenceNumber, actualSequenceNumber, "序列号应该匹配");

        // 验证停止位 (0D0A)
        assertEquals((byte) 0x0D, response[response.length - 2], "停止位第一字节应该是0x0D");
        assertEquals((byte) 0x0A, response[response.length - 1], "停止位第二字节应该是0x0A");

        // 验证CRC位置（倒数第4和第3字节）
        assertTrue(response.length >= 10, "数据包长度应该足够包含CRC");

        System.out.println("✅ concox-master规范一致性验证通过:");
        System.out.println("  数据包格式: " + bytesToHexString(response));
        System.out.println("  起始位: " + String.format("%02X%02X", response[0], response[1]));
        System.out.println("  包长度: " + String.format("%02X", response[2]));
        System.out.println("  协议号: " + String.format("%02X", response[3]));
        System.out.println("  序列号: " + String.format("%04X", actualSequenceNumber));
        System.out.println("  CRC: " + String.format("%02X%02X", response[6], response[7]));
        System.out.println("  停止位: " + String.format("%02X%02X", response[8], response[9]));
    }

    @Test
    @DisplayName("测试不同协议的响应包格式")
    void testDifferentProtocolResponses() {
        int sequenceNumber = 0x1234;

        // 测试登录响应
        byte[] loginResponse = GT06ProtocolEncoder.buildLoginResponse(sequenceNumber);
        System.out.println("登录响应: " + bytesToHexString(loginResponse));

        // 测试心跳响应
        byte[] heartbeatResponse = GT06ProtocolEncoder.buildHeartbeatResponse(sequenceNumber);
        System.out.println("心跳响应: " + bytesToHexString(heartbeatResponse));

        // 测试报警响应
        byte[] alarmResponse = GT06ProtocolEncoder.buildAlarmResponse(sequenceNumber);
        System.out.println("报警响应: " + bytesToHexString(alarmResponse));

        // 验证所有响应包都有相同的基本结构
        assertEquals(loginResponse.length, heartbeatResponse.length, "登录和心跳响应长度应该相同");
        assertEquals(loginResponse.length, alarmResponse.length, "登录和报警响应长度应该相同");

        // 验证只有协议号不同
        assertEquals(loginResponse[3], (byte) 0x01, "登录响应协议号");
        assertEquals(heartbeatResponse[3], (byte) 0x13, "心跳响应协议号");
        assertEquals(alarmResponse[3], (byte) 0x16, "报警响应协议号");

        System.out.println("✅ 不同协议响应包格式验证通过");
    }

    /**
     * 将字节数组转换为hex字符串
     */
    private String bytesToHexString(byte[] bytes) {
        return ByteBufUtils.bytesToHexString(bytes);
    }
}
