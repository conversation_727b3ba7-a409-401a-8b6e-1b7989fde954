package com.yunqu.park.iot.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.util.Date;

/**
 * IoT模块Jackson配置
 * 自定义Date类型的序列化和反序列化
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(ObjectMapper.class)
public class IotJacksonConfig {

    /**
     * 自定义Date反序列化器
     * 处理空字符串和各种日期格式
     */
    public static class CustomDateDeserializer extends JsonDeserializer<Date> {

        @Override
        public Date deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            String dateString = parser.getText();
            
            try {
                // 如果是空字符串或null，返回null
                if (StrUtil.isBlank(dateString)) {
                    return null;
                }

                // 去除前后空格
                dateString = dateString.trim();

                // 如果是空字符串，返回null
                if (dateString.isEmpty()) {
                    return null;
                }

                // 尝试解析时间戳（毫秒）
                if (dateString.matches("\\d{13}")) {
                    return new Date(Long.parseLong(dateString)); 
                }

                // 尝试解析时间戳（秒）
                if (dateString.matches("\\d{10}")) {
                    return new Date(Long.parseLong(dateString) * 1000);
                }

                // 使用hutool的DateUtil进行智能解析
                return DateUtil.parse(dateString);

            } catch (Exception e) {
                log.warn("Failed to deserialize date string '{}': {}", dateString, e.getMessage());
                return null;
            }
        }
    }

    /**
     * 配置ObjectMapper
     */
    @Bean
    @Primary
    public ObjectMapper iotObjectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper objectMapper = builder.build();
        
        // 创建自定义模块
        SimpleModule iotModule = new SimpleModule("IotModule");
        
        // 注册自定义Date反序列化器
        iotModule.addDeserializer(Date.class, new CustomDateDeserializer());
        
        // 注册模块
        objectMapper.registerModule(iotModule);
        
        log.info("IoT Jackson configuration applied successfully");
        
        return objectMapper;
    }
}
