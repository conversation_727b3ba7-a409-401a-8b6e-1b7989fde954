# IoT设备服务配置
iot:
  # TCP服务器配置
  tcp-server:
    enabled: true
    port: 8888
    boss-threads: 1
    worker-threads: 8
    so-backlog: 128
    so-keepalive: true
    tcp-nodelay: true

  # 设备管理配置
  device:
    # 心跳超时时间(秒)
    heartbeat-timeout: 300
    # 设备离线检查间隔(秒)
    offline-check-interval: 60
    # 最大重连次数
    max-reconnect-times: 3
    # 重连间隔(秒)
    reconnect-interval: 20

  # 数据处理配置
  data:
    # 定位数据批量插入大小
    location-batch-size: 100
    # 数据保留天数
    data-retention-days: 365
    # 是否启用数据压缩
    enable-compression: true

  # 报警配置
  alarm:
    # 报警类型映射(完整版本)
    type-mapping:
      0x00: "正常"
      0x01: "SOS报警"
      0x02: "断电报警"
      0x03: "震动报警"
      0x04: "进围栏报警"
      0x05: "出围栏报警"
      0x06: "超速报警"
      0x07: "高温报警"
      0x08: "低温报警"
      0x09: "位移报警"
      0x0E: "低电报警"
      0x13: "防拆报警(光感报警)"
      0x26: "急加速报警"
      0x27: "急减速报警"
      0x28: "急转弯报警"
      0x29: "碰撞报警"
      0xFA: "门关闭报警"
      0xFB: "门打开报警"
      0xFC: "AC关闭报警"
      0xFD: "AC打开报警"
      0xFE: "ACC点火报警"
      0xFF: "ACC熄火报警"

  # 安全配置
  security:
    # IP白名单
    ip-whitelist:
      enabled: false
      allowed-ips:
        - "***********/24"
        - "10.0.0.0/8"

    # 连接限制
    connection-limit:
      max-connections-per-ip: 100
      max-total-connections: 10000

    # 频率限制
    rate-limit:
      enabled: false
      requests-per-minute: 1000

    # SSL/TLS支持（可选）
    ssl:
      enabled: false
      keystore-path: "classpath:ssl/server.jks"
      keystore-password: "password"

# IoT协议配置
  protocol:
    # 是否启用严格的CRC校验（生产环境建议true）
    strict-crc-validation: false
    # 是否启用协议调试日志
    debug-enabled: true
    # 最大数据包大小（字节）
    max-packet-size: 1024
    # 连接超时时间（秒）
    connection-timeout: 300
    # 心跳间隔（秒）
    heartbeat-interval: 60
    # 是否自动注册新设备
    auto-register-device: true
    # 支持的协议版本
    supported-versions:
      - GT06
      - GT02
      - GT09
    # CRC校验配置
    crc:
      # 是否启用CRC校验
      enabled: true
      # 是否使用宽松模式（尝试多种字节序和数据范围）
      lenient-mode: true
      # CRC校验失败时是否继续处理
      continue-on-failure: true
      # 是否记录CRC校验详细日志
      verbose-logging: true

# IoT模块日志配置说明
# 注意：日志配置已集成到主应用的logback-plus.xml中
# 以下配置仅作为备用，实际日志输出由主应用控制
logging:
  level:
    # IoT模块日志级别说明：
    # - 开发环境：DEBUG级别，输出详细日志
    # - 生产环境：INFO级别，输出关键信息
    # - 具体配置在park-admin/src/main/resources/logback-plus.xml中
    com.yunqu.park.iot: INFO
