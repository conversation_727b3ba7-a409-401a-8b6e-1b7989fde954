package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.utils.CrcUtils;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * GT06协议编码器
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolEncoder extends MessageToByteEncoder<IotMessage> {

    @Override
    protected void encode(ChannelHandlerContext ctx, IotMessage msg, ByteBuf out) throws Exception {
        try {
            // 1. 写入起始位(根据协议类型选择)
            if (msg.use7979StartFlag()) {
                out.writeBytes(IotConstants.GT06Protocol.START_FLAG_7979);
            } else {
                out.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878);
            }

            // 2. 构建数据包内容
            ByteBuf content = Unpooled.buffer();
            content.writeByte(msg.getProtocol());
            if (msg.getContent() != null) {
                content.writeBytes(msg.getContent());
            }
            content.writeShort(msg.getSequenceNumber());

            // 3. 计算并写入包长度
            out.writeByte(content.readableBytes() + 2); // +2 for CRC

            // 4. 写入内容
            out.writeBytes(content);

            // 5. 计算CRC校验
            byte[] dataForCrc = new byte[out.readableBytes() - 2]; // 不包含起始位
            out.getBytes(2, dataForCrc);
            int crc = CrcUtils.calculateCrcItu(dataForCrc);
            out.writeShort(crc);

            // 6. 写入停止位
            out.writeBytes(IotConstants.GT06Protocol.STOP_FLAG);

            content.release();

            log.debug("Encoded response message: protocol=0x{}, sequenceNumber={}, length={}",
                    String.format("%02X", msg.getProtocol() & 0xFF),
                    msg.getSequenceNumber(),
                    out.readableBytes());

        } catch (Exception e) {
            log.error("Failed to encode message: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 构建登录响应包
     * @param sequenceNumber 序列号
     * @return 登录响应数据
     */
    public static byte[] buildLoginResponse(int sequenceNumber) {
        ByteBuf response = Unpooled.buffer();
        response.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878); // 起始位
        response.writeByte(0x05); // 包长度
        response.writeByte(IotConstants.GT06Protocol.PROTOCOL_LOGIN); // 协议号
        response.writeShort(sequenceNumber); // 序列号

        // 计算CRC
        byte[] dataForCrc = new byte[response.readableBytes() - 2];
        response.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        response.writeShort(crc);

        response.writeBytes(IotConstants.GT06Protocol.STOP_FLAG); // 停止位

        byte[] result = new byte[response.readableBytes()];
        response.readBytes(result);
        response.release();

        return result;
    }

    /**
     * 构建心跳响应包
     * @param sequenceNumber 序列号
     * @return 心跳响应数据
     */
    public static byte[] buildHeartbeatResponse(int sequenceNumber) {
        ByteBuf response = Unpooled.buffer();
        response.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878); // 起始位
        response.writeByte(0x05); // 包长度
        response.writeByte(IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT); // 协议号
        response.writeShort(sequenceNumber); // 序列号

        // 计算CRC
        byte[] dataForCrc = new byte[response.readableBytes() - 2];
        response.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        response.writeShort(crc);

        response.writeBytes(IotConstants.GT06Protocol.STOP_FLAG); // 停止位

        byte[] result = new byte[response.readableBytes()];
        response.readBytes(result);
        response.release();

        return result;
    }

    /**
     * 构建报警响应包
     * @param sequenceNumber 序列号
     * @return 报警响应数据
     */
    public static byte[] buildAlarmResponse(int sequenceNumber) {
        ByteBuf response = Unpooled.buffer();
        response.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878); // 起始位
        response.writeByte(0x05); // 包长度
        response.writeByte(IotConstants.GT06Protocol.PROTOCOL_ALARM); // 协议号
        response.writeShort(sequenceNumber); // 序列号

        // 计算CRC
        byte[] dataForCrc = new byte[response.readableBytes() - 2];
        response.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        response.writeShort(crc);

        response.writeBytes(IotConstants.GT06Protocol.STOP_FLAG); // 停止位

        byte[] result = new byte[response.readableBytes()];
        response.readBytes(result);
        response.release();

        return result;
    }

    /**
     * 特殊设备登录响应(S11/S11C/W15L等)
     * 需要包含UTC时间信息
     * @param sequenceNumber 序列号
     * @return 带时间的登录响应数据
     */
    public static byte[] buildLoginResponseWithTime(int sequenceNumber) {
        LocalDateTime utcTime = LocalDateTime.now(ZoneOffset.UTC);

        ByteBuf response = Unpooled.buffer();
        response.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878); // 起始位
        response.writeByte(0x0B); // 包长度
        response.writeByte(IotConstants.GT06Protocol.PROTOCOL_LOGIN); // 协议号
        response.writeShort(sequenceNumber); // 序列号

        // UTC时间 (年月日时分秒)
        response.writeByte(utcTime.getYear() % 100); // 年(后两位)
        response.writeByte(utcTime.getMonthValue()); // 月
        response.writeByte(utcTime.getDayOfMonth()); // 日
        response.writeByte(utcTime.getHour()); // 时
        response.writeByte(utcTime.getMinute()); // 分
        response.writeByte(utcTime.getSecond()); // 秒

        // 计算CRC
        byte[] dataForCrc = new byte[response.readableBytes() - 2];
        response.getBytes(2, dataForCrc);
        int crc = CrcUtils.calculateCrcItu(dataForCrc);
        response.writeShort(crc);

        response.writeBytes(IotConstants.GT06Protocol.STOP_FLAG); // 停止位

        byte[] result = new byte[response.readableBytes()];
        response.readBytes(result);
        response.release();

        return result;
    }
}
