package com.yunqu.park.iot.netty.codec;

import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import com.yunqu.park.iot.utils.ByteBufOptimizer;
import com.yunqu.park.iot.utils.CrcUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * GT06协议编码器
 *
 * <AUTHOR>
 */
@Slf4j
public class GT06ProtocolEncoder extends MessageToByteEncoder<IotMessage> {

    @Override
    protected void encode(ChannelHandlerContext ctx, IotMessage msg, ByteBuf out) throws Exception {
        String remoteAddress = ctx.channel().remoteAddress() != null ?
                              ctx.channel().remoteAddress().toString() : "unknown";
        ByteBuf content = null;

        try {
            log.debug("[PROTOCOL-ENCODE] 🔧 开始编码响应消息: RemoteAddress={}, Protocol=0x{}, SequenceNumber={}",
                     remoteAddress, String.format("%02X", msg.getProtocol() & 0xFF), msg.getSequenceNumber());

            // 1. 写入起始位(根据协议类型选择)
            if (msg.use7979StartFlag()) {
                out.writeBytes(IotConstants.GT06Protocol.START_FLAG_7979);
                log.debug("[PROTOCOL-ENCODE] 使用7979起始位");
            } else {
                out.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878);
                log.debug("[PROTOCOL-ENCODE] 使用7878起始位");
            }

            // 2. 修正：按照concox-master规范构建数据包
            // 构建prefix部分（用于CRC计算）
            content = ByteBufOptimizer.createPooledBuffer(64);

            // 计算包长度：协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            int contentLength = (msg.getContent() != null) ? msg.getContent().length : 0;
            int packetLength = 1 + contentLength + 2 + 2;
            content.writeByte(packetLength);
            log.debug("[PROTOCOL-ENCODE] 包长度: {}", packetLength);

            // 协议号
            content.writeByte(msg.getProtocol());

            // 内容数据
            if (msg.getContent() != null && msg.getContent().length > 0) {
                content.writeBytes(msg.getContent());
                log.debug("[PROTOCOL-ENCODE] 写入内容数据: {}字节", msg.getContent().length);
            }

            // 序列号
            content.writeShort(msg.getSequenceNumber());

            // 3. 修正：计算CRC（只对prefix部分，符合concox规范）
            byte[] prefixData = new byte[content.readableBytes()];
            content.readBytes(prefixData);
            int crc = CrcUtils.calculateCrcItu(prefixData);
            log.debug("[PROTOCOL-ENCODE] CRC校验值: 0x{}", String.format("%04X", crc));

            // 4. 写入完整数据包
            out.writeBytes(prefixData);  // prefix数据
            out.writeShort(crc);         // CRC
            out.writeBytes(IotConstants.GT06Protocol.STOP_FLAG); // 停止位

            log.info("[PROTOCOL-ENCODE] ✅ 响应消息编码成功: RemoteAddress={}, Protocol=0x{}, SequenceNumber={}, TotalLength={}",
                    remoteAddress, String.format("%02X", msg.getProtocol() & 0xFF),
                    msg.getSequenceNumber(), out.readableBytes());

        } catch (Exception e) {
            log.error("[PROTOCOL-ENCODE] ❌ 编码响应消息失败: RemoteAddress={}, Protocol=0x{}, Error={}",
                     remoteAddress, String.format("%02X", msg.getProtocol() & 0xFF), e.getMessage(), e);
            throw e;
        } finally {
            // 优化：确保ByteBuf正确释放
            ByteBufOptimizer.safeRelease(content);
        }
    }

    /**
     * 修正：按照concox-master规范构建响应包
     * 格式：起始位(7878) + 包长度 + 协议号 + [内容] + 序列号 + CRC + 停止位(0D0A)
     * @param protocol 协议号
     * @param sequenceNumber 序列号
     * @param content 内容数据（可为null）
     * @return 响应数据
     */
    private static byte[] buildResponsePacket(byte protocol, int sequenceNumber, byte[] content) {
        ByteBuf response = null;
        try {
            response = ByteBufOptimizer.createPooledBuffer(32);

            // 1. 构建prefix部分（用于CRC计算）
            ByteBuf prefix = ByteBufOptimizer.createPooledBuffer(16);

            // 计算包长度：协议号(1) + 内容(N) + 序列号(2) + CRC(2)
            int contentLength = (content != null) ? content.length : 0;
            int packetLength = 1 + contentLength + 2 + 2; // 协议号 + 内容 + 序列号 + CRC
            prefix.writeByte(packetLength);

            // 协议号
            prefix.writeByte(protocol);

            // 内容数据
            if (content != null && content.length > 0) {
                prefix.writeBytes(content);
            }

            // 序列号
            prefix.writeShort(sequenceNumber);

            // 2. 计算CRC（只对prefix部分，符合concox-master规范）
            byte[] prefixData = new byte[prefix.readableBytes()];
            prefix.readBytes(prefixData);
            int crc = CrcUtils.calculateCrcItu(prefixData);

            // 3. 构建完整响应包
            // 起始位
            response.writeBytes(IotConstants.GT06Protocol.START_FLAG_7878);

            // prefix数据
            response.writeBytes(prefixData);

            // CRC
            response.writeShort(crc);

            // 停止位
            response.writeBytes(IotConstants.GT06Protocol.STOP_FLAG);

            // 复制结果
            byte[] result = new byte[response.readableBytes()];
            response.readBytes(result);

            log.debug("[PROTOCOL-ENCODE] 构建响应包(concox规范): Protocol=0x{}, SequenceNumber={}, PacketLength={}, TotalLength={}",
                     String.format("%02X", protocol & 0xFF), sequenceNumber, packetLength, result.length);

            ByteBufOptimizer.safeRelease(prefix);
            return result;

        } catch (Exception e) {
            log.error("[PROTOCOL-ENCODE] 构建响应包失败: Protocol=0x{}, Error={}",
                     String.format("%02X", protocol & 0xFF), e.getMessage(), e);
            throw new RuntimeException("构建响应包失败", e);
        } finally {
            ByteBufOptimizer.safeRelease(response);
        }
    }

    /**
     * 构建登录响应包
     * @param sequenceNumber 序列号
     * @return 登录响应数据
     */
    public static byte[] buildLoginResponse(int sequenceNumber) {
        return buildResponsePacket(IotConstants.GT06Protocol.PROTOCOL_LOGIN, sequenceNumber, null);
    }

    /**
     * 构建心跳响应包
     * @param sequenceNumber 序列号
     * @return 心跳响应数据
     */
    public static byte[] buildHeartbeatResponse(int sequenceNumber) {
        return buildResponsePacket(IotConstants.GT06Protocol.PROTOCOL_HEARTBEAT, sequenceNumber, null);
    }

    /**
     * 构建报警响应包
     * @param sequenceNumber 序列号
     * @return 报警响应数据
     */
    public static byte[] buildAlarmResponse(int sequenceNumber) {
        return buildResponsePacket(IotConstants.GT06Protocol.PROTOCOL_ALARM, sequenceNumber, null);
    }

    /**
     * 特殊设备登录响应(S11/S11C/W15L等)
     * 需要包含UTC时间信息
     * @param sequenceNumber 序列号
     * @return 带时间的登录响应数据
     */
    public static byte[] buildLoginResponseWithTime(int sequenceNumber) {
        try {
            LocalDateTime utcTime = LocalDateTime.now(ZoneOffset.UTC);

            // 构建时间内容
            byte[] timeContent = new byte[6];
            timeContent[0] = (byte) (utcTime.getYear() % 100); // 年(后两位)
            timeContent[1] = (byte) utcTime.getMonthValue();   // 月
            timeContent[2] = (byte) utcTime.getDayOfMonth();   // 日
            timeContent[3] = (byte) utcTime.getHour();         // 时
            timeContent[4] = (byte) utcTime.getMinute();       // 分
            timeContent[5] = (byte) utcTime.getSecond();       // 秒

            log.debug("[PROTOCOL-ENCODE] 构建带时间的登录响应: UTC时间={}-{:02d}-{:02d} {:02d}:{:02d}:{:02d}",
                     utcTime.getYear(), utcTime.getMonthValue(), utcTime.getDayOfMonth(),
                     utcTime.getHour(), utcTime.getMinute(), utcTime.getSecond());

            return buildResponsePacket(IotConstants.GT06Protocol.PROTOCOL_LOGIN, sequenceNumber, timeContent);

        } catch (Exception e) {
            log.error("[PROTOCOL-ENCODE] 构建带时间的登录响应失败: {}", e.getMessage(), e);
            // 失败时返回普通登录响应
            return buildLoginResponse(sequenceNumber);
        }
    }

    /**
     * 优化：获取编码器统计信息
     * @return 统计信息字符串
     */
    public static String getEncoderStatistics() {
        return String.format("ByteBuf池统计: %s", ByteBufOptimizer.getPoolStatistics());
    }
}
