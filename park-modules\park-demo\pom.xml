<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunqu.park</groupId>
        <artifactId>park-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>park-demo</artifactId>

    <description>
        demo模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-ratelimiter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunqu.park</groupId>
            <artifactId>park-common-websocket</artifactId>
        </dependency>

    </dependencies>

</project>
