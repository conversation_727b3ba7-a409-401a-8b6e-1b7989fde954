package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.config.IotLogConfig;
import com.yunqu.park.iot.netty.manager.DeviceConnectionManager;
import com.yunqu.park.iot.utils.IotLogUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * IoT监控控制器
 * 提供设备连接状态和日志统计信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/monitor")
public class IotMonitorController extends BaseController {

    private final DeviceConnectionManager connectionManager;
    private final IotLogConfig iotLogConfig;

    /**
     * 获取设备连接统计信息
     */
    @SaCheckPermission("iot:monitor:connection")
    @GetMapping("/connections")
    public R<Object> getConnectionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("onlineDeviceCount", connectionManager.getOnlineDeviceCount());
        stats.put("onlineDeviceImeis", connectionManager.getOnlineDeviceImeis());
        stats.put("timestamp", System.currentTimeMillis());
        
        return R.ok(stats);
    }

    /**
     * 获取IoT系统统计信息
     */
    @SaCheckPermission("iot:monitor:statistics")
    @GetMapping("/statistics")
    public R<String> getSystemStatistics() {
        String statistics = IotLogUtils.getStatistics();
        return R.ok(statistics);
    }

    /**
     * 重置统计计数器
     */
    @SaCheckPermission("iot:monitor:reset")
    @PostMapping("/statistics/reset")
    public R<Void> resetStatistics() {
        IotLogUtils.resetStatistics();
        return R.ok();
    }

    /**
     * 输出当前统计信息到日志
     */
    @SaCheckPermission("iot:monitor:log")
    @PostMapping("/statistics/log")
    public R<Void> logStatistics() {
        IotLogUtils.logStatistics();
        return R.ok();
    }

    /**
     * 检查设备是否在线
     */
    @SaCheckPermission("iot:monitor:device")
    @GetMapping("/device/{imei}/online")
    public R<Boolean> isDeviceOnline(@PathVariable String imei) {
        boolean online = connectionManager.isDeviceOnline(imei);
        return R.ok(online);
    }

    /**
     * 获取系统健康状态
     */
    @SaCheckPermission("iot:monitor:health")
    @GetMapping("/health")
    public R<Object> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        
        // 连接状态
        int onlineCount = connectionManager.getOnlineDeviceCount();
        health.put("onlineDevices", onlineCount);
        health.put("connectionStatus", onlineCount > 0 ? "HEALTHY" : "NO_CONNECTIONS");
        
        // 系统状态
        health.put("systemStatus", "RUNNING");
        health.put("timestamp", System.currentTimeMillis());
        
        // 统计信息
        health.put("statistics", IotLogUtils.getStatistics());
        
        return R.ok(health);
    }

    /**
     * 获取设备详细信息
     */
    @SaCheckPermission("iot:monitor:device")
    @GetMapping("/device/{imei}/details")
    public R<Object> getDeviceDetails(@PathVariable String imei) {
        Map<String, Object> details = new HashMap<>();

        boolean online = connectionManager.isDeviceOnline(imei);
        details.put("imei", imei);
        details.put("online", online);
        details.put("timestamp", System.currentTimeMillis());

        if (online) {
            details.put("status", "CONNECTED");
        } else {
            details.put("status", "DISCONNECTED");
        }

        return R.ok(details);
    }

    /**
     * 获取日志配置状态
     */
    @SaCheckPermission("iot:monitor:log")
    @GetMapping("/log/config")
    public R<Object> getLogConfigStatus() {
        IotLogConfig.LogConfigStatus status = iotLogConfig.getLogConfigStatus();
        return R.ok(status);
    }
}
