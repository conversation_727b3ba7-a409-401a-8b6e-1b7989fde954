package com.yunqu.park.iot.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.yunqu.park.common.core.domain.R;
import com.yunqu.park.common.log.annotation.Log;
import com.yunqu.park.common.log.enums.BusinessType;
import com.yunqu.park.common.web.core.BaseController;
import com.yunqu.park.iot.service.IIotCommandService;
import com.yunqu.park.iot.netty.handler.CommandResponseHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * IoT设备指令控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/iot/command")
public class IotCommandController extends BaseController {

    private final IIotCommandService iotCommandService;
    private final CommandResponseHandler commandResponseHandler;

    /**
     * 发送设备重启指令
     */
    @SaCheckPermission("iot:command:restart")
    @Log(title = "设备重启指令", businessType = BusinessType.OTHER)
    @PostMapping("/restart/{imei}")
    public R<Void> restartDevice(@NotBlank(message = "IMEI不能为空") @PathVariable String imei) {
        return toAjax(iotCommandService.sendRestartCommand(imei));
    }

    /**
     * 发送设备复位指令
     */
    @SaCheckPermission("iot:command:reset")
    @Log(title = "设备复位指令", businessType = BusinessType.OTHER)
    @PostMapping("/reset/{imei}")
    public R<Void> resetDevice(@NotBlank(message = "IMEI不能为空") @PathVariable String imei) {
        return toAjax(iotCommandService.sendResetCommand(imei));
    }

    /**
     * 设置设备上报间隔
     */
    @SaCheckPermission("iot:command:interval")
    @Log(title = "设置上报间隔", businessType = BusinessType.UPDATE)
    @PostMapping("/interval/{imei}")
    public R<Void> setReportInterval(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                                     @NotNull(message = "间隔时间不能为空") @RequestParam Integer interval) {
        return toAjax(iotCommandService.setReportInterval(imei, interval));
    }

    /**
     * 设置设备APN参数
     */
    @SaCheckPermission("iot:command:apn")
    @Log(title = "设置APN参数", businessType = BusinessType.UPDATE)
    @PostMapping("/apn/{imei}")
    public R<Void> setApnConfig(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                                @NotBlank(message = "APN不能为空") @RequestParam String apn,
                                @RequestParam(required = false) String username,
                                @RequestParam(required = false) String password) {
        return toAjax(iotCommandService.setApnConfig(imei, apn, username, password));
    }

    /**
     * 设置设备服务器地址
     */
    @SaCheckPermission("iot:command:server")
    @Log(title = "设置服务器地址", businessType = BusinessType.UPDATE)
    @PostMapping("/server/{imei}")
    public R<Void> setServerAddress(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                                    @NotBlank(message = "服务器地址不能为空") @RequestParam String serverIp,
                                    @NotNull(message = "端口不能为空") @RequestParam Integer port) {
        return toAjax(iotCommandService.setServerAddress(imei, serverIp, port));
    }

    /**
     * 查询设备状态
     */
    @SaCheckPermission("iot:command:status")
    @Log(title = "查询设备状态", businessType = BusinessType.OTHER)
    @PostMapping("/status/{imei}")
    public R<Void> queryDeviceStatus(@NotBlank(message = "IMEI不能为空") @PathVariable String imei) {
        return toAjax(iotCommandService.queryDeviceStatus(imei));
    }

    /**
     * 设置设备时区
     */
    @SaCheckPermission("iot:command:timezone")
    @Log(title = "设置设备时区", businessType = BusinessType.UPDATE)
    @PostMapping("/timezone/{imei}")
    public R<Void> setTimezone(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                               @NotNull(message = "时区不能为空") @RequestParam Integer timezone) {
        return toAjax(iotCommandService.setTimezone(imei, timezone));
    }

    /**
     * 设置设备工作模式
     */
    @SaCheckPermission("iot:command:mode")
    @Log(title = "设置工作模式", businessType = BusinessType.UPDATE)
    @PostMapping("/mode/{imei}")
    public R<Void> setWorkMode(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                               @NotNull(message = "工作模式不能为空") @RequestParam Integer mode) {
        return toAjax(iotCommandService.setWorkMode(imei, mode));
    }

    /**
     * 发送自定义指令
     */
    @SaCheckPermission("iot:command:custom")
    @Log(title = "发送自定义指令", businessType = BusinessType.OTHER)
    @PostMapping("/custom/{imei}")
    public R<Void> sendCustomCommand(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                                     @NotBlank(message = "指令内容不能为空") @RequestParam String command) {
        return toAjax(iotCommandService.sendCustomCommand(imei, command));
    }

    /**
     * 获取设备指令发送历史
     */
    @SaCheckPermission("iot:command:history")
    @GetMapping("/history/{imei}")
    public R<Object> getCommandHistory(@NotBlank(message = "IMEI不能为空") @PathVariable String imei,
                                       @RequestParam(defaultValue = "10") Integer limit) {
        return R.ok(iotCommandService.getCommandHistory(imei, limit));
    }

    /**
     * 批量发送指令
     */
    @SaCheckPermission("iot:command:batch")
    @Log(title = "批量发送指令", businessType = BusinessType.OTHER)
    @PostMapping("/batch")
    public R<Object> sendBatchCommand(@RequestBody BatchCommandRequest request) {
        return R.ok(iotCommandService.sendBatchCommand(request));
    }

    /**
     * 获取设备指令响应
     */
    @SaCheckPermission("iot:command:response")
    @GetMapping("/response/{imei}")
    public R<Object> getCommandResponse(@NotBlank(message = "IMEI不能为空") @PathVariable String imei) {
        return R.ok(commandResponseHandler.getLatestCommandResponse(imei));
    }

    /**
     * 清除设备指令响应缓存
     */
    @SaCheckPermission("iot:command:response")
    @DeleteMapping("/response/{imei}")
    public R<Void> clearCommandResponse(@NotBlank(message = "IMEI不能为空") @PathVariable String imei) {
        commandResponseHandler.clearCommandResponse(imei);
        return R.ok();
    }

    /**
     * 批量指令请求对象
     */
    public static class BatchCommandRequest {
        private String[] imeis;
        private String commandType;
        private Object commandData;
        
        // getters and setters
        public String[] getImeis() { return imeis; }
        public void setImeis(String[] imeis) { this.imeis = imeis; }
        public String getCommandType() { return commandType; }
        public void setCommandType(String commandType) { this.commandType = commandType; }
        public Object getCommandData() { return commandData; }
        public void setCommandData(Object commandData) { this.commandData = commandData; }
    }
}
