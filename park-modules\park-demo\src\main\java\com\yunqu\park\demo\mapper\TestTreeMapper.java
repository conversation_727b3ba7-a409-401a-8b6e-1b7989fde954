package com.yunqu.park.demo.mapper;

import com.yunqu.park.common.mybatis.annotation.DataColumn;
import com.yunqu.park.common.mybatis.annotation.DataPermission;
import com.yunqu.park.common.mybatis.core.mapper.BaseMapperPlus;
import com.yunqu.park.demo.domain.TestTree;
import com.yunqu.park.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTree, TestTreeVo> {

}
