package com.yunqu.park.iot.netty.handler;

import com.yunqu.park.common.redis.utils.RedisUtils;
import com.yunqu.park.iot.constant.IotConstants;
import com.yunqu.park.iot.netty.protocol.IotMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 设备指令响应处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandResponseHandler {

    private static final String COMMAND_RESPONSE_PREFIX = "iot:command:response:";

    /**
     * 处理设备指令响应
     * @param message 响应消息
     * @param imei 设备IMEI号
     */
    public void handleCommandResponse(IotMessage message, String imei) {
        try {
            if (message.getProtocol() == IotConstants.GT06Protocol.PROTOCOL_TERMINAL_RESPONSE) {
                // 解析指令响应
                parseTerminalResponse(message, imei);
            } else {
                log.debug("Received non-command response from device: {}", imei);
            }
        } catch (Exception e) {
            log.error("Failed to handle command response from device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析终端指令响应
     * @param message 响应消息
     * @param imei 设备IMEI号
     */
    private void parseTerminalResponse(IotMessage message, String imei) {
        try {
            byte[] content = message.getContent();
            if (content == null || content.length < 1) {
                log.warn("Invalid terminal response content from device: {}", imei);
                return;
            }

            // 解析响应状态
            byte responseStatus = content[0];
            String status = parseResponseStatus(responseStatus);
            
            // 缓存响应结果
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("status", status);
            responseData.put("statusCode", responseStatus & 0xFF);
            responseData.put("timestamp", System.currentTimeMillis());
            responseData.put("sequenceNumber", message.getSequenceNumber());
            
            // 如果有额外的响应数据
            if (content.length > 1) {
                byte[] extraData = new byte[content.length - 1];
                System.arraycopy(content, 1, extraData, 0, extraData.length);
                responseData.put("extraData", extraData);
            }
            
            RedisUtils.setCacheObject(cacheKey, responseData, Duration.ofMinutes(10));
            
            log.info("Command response received from device {}: status={}, sequenceNumber={}", 
                    imei, status, message.getSequenceNumber());
            
        } catch (Exception e) {
            log.error("Failed to parse terminal response from device {}: {}", imei, e.getMessage(), e);
        }
    }

    /**
     * 解析响应状态码
     * @param statusCode 状态码
     * @return 状态描述
     */
    private String parseResponseStatus(byte statusCode) {
        return switch (statusCode & 0xFF) {
            case 0x00 -> "SUCCESS";
            case 0x01 -> "FAILED";
            case 0x02 -> "INVALID_COMMAND";
            case 0x03 -> "UNSUPPORTED";
            case 0x04 -> "PARAMETER_ERROR";
            case 0x05 -> "DEVICE_BUSY";
            default -> "UNKNOWN(" + String.format("0x%02X", statusCode & 0xFF) + ")";
        };
    }

    /**
     * 获取设备最新的指令响应
     * @param imei 设备IMEI号
     * @return 响应数据
     */
    public Object getLatestCommandResponse(String imei) {
        try {
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            return RedisUtils.getCacheObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to get command response for device {}: {}", imei, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清除设备指令响应缓存
     * @param imei 设备IMEI号
     */
    public void clearCommandResponse(String imei) {
        try {
            String cacheKey = COMMAND_RESPONSE_PREFIX + imei;
            RedisUtils.deleteObject(cacheKey);
        } catch (Exception e) {
            log.error("Failed to clear command response for device {}: {}", imei, e.getMessage(), e);
        }
    }
}
