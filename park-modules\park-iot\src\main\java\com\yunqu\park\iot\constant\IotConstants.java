package com.yunqu.park.iot.constant;

/**
 * IoT模块常量
 *
 * <AUTHOR>
 */
public class IotConstants {

    /**
     * GT06协议常量
     */
    public static class GT06Protocol {
        /** 起始位 0x7878 */
        public static final byte[] START_FLAG_7878 = {0x78, 0x78};
        
        /** 起始位 0x7979 */
        public static final byte[] START_FLAG_7979 = {0x79, 0x79};
        
        /** 停止位 0x0D0A */
        public static final byte[] STOP_FLAG = {0x0D, 0x0A};
        
        /** 最小包长度 */
        public static final int MIN_PACKET_LENGTH = 10;
        
        /** 协议号 - 登录包 */
        public static final byte PROTOCOL_LOGIN = 0x01;
        
        /** 协议号 - 定位数据包 */
        public static final byte PROTOCOL_LOCATION = 0x12;
        
        /** 协议号 - 心跳包 */
        public static final byte PROTOCOL_HEARTBEAT = 0x13;
        
        /** 协议号 - 报警包 */
        public static final byte PROTOCOL_ALARM = 0x16;
        
        /** 协议号 - 查询地址信息 */
        public static final byte PROTOCOL_ADDRESS_QUERY = 0x1A;
        
        /** 协议号 - LBS多基站信息 */
        public static final byte PROTOCOL_LBS = 0x18;
        
        /** 协议号 - LBS+WIFI信息 */
        public static final byte PROTOCOL_LBS_WIFI = 0x2C;
        
        /** 协议号 - IMSI号上报 */
        public static final byte PROTOCOL_IMSI = (byte) 0x90;
        
        /** 协议号 - ICCID号上报 */
        public static final byte PROTOCOL_ICCID = (byte) 0x94;
        
        /** 协议号 - 录音协议包 */
        public static final byte PROTOCOL_RECORD = (byte) 0x8D;
        
        /** 协议号 - 服务器指令下发 */
        public static final byte PROTOCOL_SERVER_CMD = (byte) 0x80;
        
        /** 协议号 - 终端指令响应 */
        public static final byte PROTOCOL_TERMINAL_RESPONSE = 0x15;
    }

    /**
     * 设备状态常量
     */
    public static class DeviceStatus {
        /** 离线 */
        public static final String OFFLINE = "0";
        
        /** 在线 */
        public static final String ONLINE = "1";
        
        /** 休眠 */
        public static final String SLEEP = "2";
    }

    /**
     * GPS状态常量
     */
    public static class GpsStatus {
        /** 未定位 */
        public static final String NOT_POSITIONED = "0";
        
        /** 已定位 */
        public static final String POSITIONED = "1";
    }

    /**
     * ACC状态常量
     */
    public static class AccStatus {
        /** 关闭 */
        public static final String OFF = "0";
        
        /** 开启 */
        public static final String ON = "1";
    }

    /**
     * 报警状态常量
     */
    public static class AlarmStatus {
        /** 未处理 */
        public static final String UNHANDLED = "0";
        
        /** 已处理 */
        public static final String HANDLED = "1";
    }

    /**
     * 缓存键常量
     */
    public static class CacheKeys {
        /** 设备状态缓存前缀 */
        public static final String DEVICE_STATUS_PREFIX = "iot:device:status:";
        
        /** 设备连接信息缓存前缀 */
        public static final String DEVICE_CONNECTION_PREFIX = "iot:device:connection:";
        
        /** 在线设备集合 */
        public static final String ONLINE_DEVICES = "iot:devices:online";
        
        /** 最新位置缓存前缀 */
        public static final String LATEST_LOCATION_PREFIX = "iot:location:latest:";
    }

    /**
     * 报警类型映射
     */
    public static class AlarmType {
        public static final int NORMAL = 0x00;          // 正常
        public static final int SOS = 0x01;             // SOS报警
        public static final int POWER_CUT = 0x02;       // 断电报警
        public static final int VIBRATION = 0x03;       // 震动报警
        public static final int FENCE_IN = 0x04;        // 进围栏报警
        public static final int FENCE_OUT = 0x05;       // 出围栏报警
        public static final int OVERSPEED = 0x06;       // 超速报警
        public static final int HIGH_TEMP = 0x07;       // 高温报警
        public static final int LOW_TEMP = 0x08;        // 低温报警
        public static final int DISPLACEMENT = 0x09;    // 位移报警
        public static final int LOW_BATTERY = 0x0E;     // 低电报警
        public static final int TAMPER = 0x13;          // 防拆报警(光感报警)
        public static final int RAPID_ACCELERATION = 0x26; // 急加速报警
        public static final int RAPID_DECELERATION = 0x27; // 急减速报警
        public static final int SHARP_TURN = 0x28;      // 急转弯报警
        public static final int COLLISION = 0x29;       // 碰撞报警
        public static final int DOOR_CLOSE = 0xFA;      // 门关闭报警
        public static final int DOOR_OPEN = 0xFB;       // 门打开报警
        public static final int AC_CLOSE = 0xFC;        // AC关闭报警
        public static final int AC_OPEN = 0xFD;         // AC打开报警
        public static final int ACC_ON = 0xFE;          // ACC点火报警
        public static final int ACC_OFF = 0xFF;         // ACC熄火报警
    }
}
