const parser = require('./tcp/parser');
const helpers = require('./tcp/parser/helpers');

/**
 * 分析数据内容
 * @param {string} data - 要分析的数据
 */
function analyzeData(data) {
    console.log('=== 数据分析开始 ===');
    console.log('原始数据:', data);
    console.log('数据长度:', data.length);
    console.log('数据类型:', typeof data);
    
    // 1. 检查是否为完整的GT06协议包
    console.log('\n--- GT06协议包检查 ---');
    const protocolResult = parser(data);
    if (protocolResult) {
        console.log('✅ 识别为GT06协议包:', protocolResult);
        return protocolResult;
    } else {
        console.log('❌ 不是完整的GT06协议包');
    }
    
    // 2. 尝试作为IMEI解析
    console.log('\n--- IMEI分析 ---');
    if (data.length === 15 || data.length === 16) {
        console.log('可能的IMEI:', data);
        
        // IMEI校验
        if (data.length === 15) {
            const checkDigit = calculateIMEICheckDigit(data.substring(0, 14));
            console.log('IMEI校验位计算:', checkDigit);
            console.log('IMEI校验结果:', data[14] == checkDigit ? '✅ 有效' : '❌ 无效');
        }
        
        // 解析IMEI组成部分
        if (data.length >= 14) {
            const tac = data.substring(0, 8);  // Type Allocation Code
            const snr = data.substring(8, 14); // Serial Number
            const cd = data.length === 15 ? data.substring(14, 15) : ''; // Check Digit
            
            console.log('TAC (设备型号):', tac);
            console.log('SNR (序列号):', snr);
            if (cd) console.log('校验位:', cd);
        }
    }
    
    // 3. 尝试作为十六进制数据解析
    console.log('\n--- 十六进制数据分析 ---');
    if (/^[0-9A-Fa-f]+$/.test(data)) {
        console.log('✅ 有效的十六进制数据');
        
        // 转换为字节数组
        const bytes = [];
        for (let i = 0; i < data.length; i += 2) {
            bytes.push(parseInt(data.substr(i, 2), 16));
        }
        console.log('字节数组:', bytes);
        console.log('字节数组(十六进制):', bytes.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));
        
        // 尝试解析为不同的数据类型
        if (bytes.length >= 4) {
            // 32位整数
            const int32 = (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3];
            console.log('32位整数 (大端):', int32);
            
            const int32le = (bytes[3] << 24) | (bytes[2] << 16) | (bytes[1] << 8) | bytes[0];
            console.log('32位整数 (小端):', int32le);
        }
        
        if (bytes.length >= 8) {
            // 64位整数 (JavaScript精度限制)
            const high = (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3];
            const low = (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7];
            const int64 = high * 0x100000000 + low;
            console.log('64位整数 (大端):', int64);
        }
        
        // 尝试解析为ASCII
        const ascii = bytes.map(b => b >= 32 && b <= 126 ? String.fromCharCode(b) : '.').join('');
        console.log('ASCII解析:', ascii);
        
    } else {
        console.log('❌ 不是有效的十六进制数据');
    }
    
    // 4. 尝试作为十进制数字解析
    console.log('\n--- 十进制数字分析 ---');
    if (/^\d+$/.test(data)) {
        console.log('✅ 有效的十进制数字');
        const number = parseInt(data);
        console.log('数值:', number);
        
        // 检查是否为时间戳
        if (number > 1000000000 && number < 9999999999) {
            const date = new Date(number * 1000);
            console.log('Unix时间戳解析:', date.toISOString());
        } else if (number > 1000000000000 && number < 9999999999999) {
            const date = new Date(number);
            console.log('毫秒时间戳解析:', date.toISOString());
        }
        
        // 转换为十六进制
        console.log('十六进制表示:', number.toString(16).toUpperCase());
        
        // 转换为二进制
        console.log('二进制表示:', number.toString(2));
    }
    
    // 5. 尝试分段解析
    console.log('\n--- 分段解析 ---');
    if (data.length >= 8) {
        const segments = [];
        for (let i = 0; i < data.length; i += 2) {
            segments.push(data.substr(i, 2));
        }
        console.log('2字节分段:', segments);
        
        const segments4 = [];
        for (let i = 0; i < data.length; i += 4) {
            segments4.push(data.substr(i, 4));
        }
        console.log('4字节分段:', segments4);
    }
    
    console.log('\n=== 数据分析结束 ===');
}

/**
 * 计算IMEI校验位
 */
function calculateIMEICheckDigit(imei14) {
    let sum = 0;
    for (let i = 0; i < 14; i++) {
        let digit = parseInt(imei14[i]);
        if (i % 2 === 1) {
            digit *= 2;
            if (digit > 9) {
                digit = Math.floor(digit / 10) + (digit % 10);
            }
        }
        sum += digit;
    }
    return (10 - (sum % 10)) % 10;
}

// 分析目标数据
const targetData = '5399471429026601';
analyzeData(targetData);

// 如果数据需要添加GT06协议包装，尝试构造完整包
console.log('\n=== 尝试构造GT06协议包 ===');
const testPackets = [
    // 假设是IMEI的登录包
    `78781101${targetData}0001001f49170d0a`,
    // 假设是其他协议的数据部分
    `787810${targetData}0d0a`,
];

testPackets.forEach((packet, index) => {
    console.log(`\n测试包 ${index + 1}: ${packet}`);
    const result = parser(packet);
    if (result) {
        console.log('解析结果:', JSON.stringify(result, null, 2));
    } else {
        console.log('解析失败');
    }
});
